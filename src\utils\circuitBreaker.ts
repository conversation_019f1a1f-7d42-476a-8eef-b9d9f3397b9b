/**
 * Circuit Breaker Pattern Implementation
 * 
 * This utility implements the circuit breaker pattern to prevent cascading failures
 * when external services (like Redis) are experiencing issues.
 * 
 * The circuit breaker has three states:
 * - CLOSED: Normal operation, requests pass through
 * - OPEN: Service is failing, requests are immediately rejected
 * - HALF_OPEN: Testing if the service has recovered
 */

import logger from './logger';

// Attack mode state for circuit breaker logging
let isCircuitBreakerAttackMode = false;
let lastCircuitBreakerAttackCheck = 0;
const CIRCUIT_BREAKER_ATTACK_DURATION = 300000; // 5 minutes

/**
 * Enable attack mode to reduce circuit breaker logging overhead
 */
export const enableCircuitBreakerAttackMode = (): void => {
  if (!isCircuitBreakerAttackMode) {
    isCircuitBreakerAttackMode = true;
    console.log('[CIRCUIT BREAKER] ATTACK MODE ENABLED - reducing logging overhead');
  }
  lastCircuitBreakerAttackCheck = Date.now();
};

/**
 * Check if we should exit attack mode
 */
const checkCircuitBreakerAttackModeExpiry = (): void => {
  if (isCircuitBreakerAttackMode && (Date.now() - lastCircuitBreakerAttackCheck > CIRCUIT_BREAKER_ATTACK_DURATION)) {
    isCircuitBreakerAttackMode = false;
    console.log('[CIRCUIT BREAKER] ATTACK MODE DISABLED - resuming normal logging');
  }
};

export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private readonly name: string,
    private readonly failureThreshold: number = 5,
    private readonly resetTimeout: number = 30000 // 30 seconds
  ) {
    // Only log initialization in non-attack mode
    if (!isCircuitBreakerAttackMode) {
      logger.info(`Circuit breaker "${name}" initialized with threshold ${failureThreshold} and reset timeout ${resetTimeout}ms`);
    }
  }
  
  /**
   * Execute a function with circuit breaker protection
   * @param fn The function to execute
   * @param fallback The fallback function to execute if the circuit is open
   * @returns The result of the function or fallback
   */
  public async execute<T>(fn: () => Promise<T>, fallback: () => Promise<T>): Promise<T> {
    // Check attack mode expiry
    checkCircuitBreakerAttackModeExpiry();

    if (this.state === 'OPEN') {
      // Check if it's time to try again
      const now = Date.now();
      if (now - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        // Only log state changes in non-attack mode
        if (!isCircuitBreakerAttackMode) {
          logger.debug(`Circuit "${this.name}" changed from OPEN to HALF_OPEN`);
        }
      } else {
        // Circuit is open, use fallback
        return fallback();
      }
    }
    
    try {
      const result = await fn();
      
      // If we're in HALF_OPEN and succeeded, reset the circuit
      if (this.state === 'HALF_OPEN') {
        this.reset();
      }
      
      return result;
    } catch (error) {
      this.failures++;
      this.lastFailureTime = Date.now();

      // If we've reached the threshold, open the circuit
      if (this.failures >= this.failureThreshold) {
        this.state = 'OPEN';
        // Only log state changes in non-attack mode or for critical failures
        if (!isCircuitBreakerAttackMode) {
          logger.warn(`Circuit "${this.name}" changed to OPEN after ${this.failures} failures`);
        }
      }

      // Reduce error logging during attack mode - only log every 10th failure
      if (!isCircuitBreakerAttackMode || this.failures % 10 === 0) {
        logger.error(`Circuit "${this.name}" failure ${this.failures}/${this.failureThreshold}:`,
          error instanceof Error ? error : new Error(String(error)));
      }

      return fallback();
    }
  }
  
  /**
   * Reset the circuit breaker to closed state
   */
  public reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    // Only log reset in non-attack mode
    if (!isCircuitBreakerAttackMode) {
      logger.debug(`Circuit "${this.name}" reset to CLOSED`);
    }
  }
  
  /**
   * Get the current state of the circuit breaker
   */
  public getState(): string {
    return this.state;
  }
  
  /**
   * Get the number of failures
   */
  public getFailures(): number {
    return this.failures;
  }
}

// Create Redis circuit breaker instance
export const redisCircuitBreaker = new CircuitBreaker('redis', 3, 60000);

// Create Database circuit breaker instance with region-aware settings
const region = process.env.REGION || 'unknown';
const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                           region.toLowerCase().includes('in') ||
                           region.toLowerCase().includes('hk') ||
                           region.toLowerCase().includes('hong');

// More tolerant settings for high-latency regions
const dbFailureThreshold = isHighLatencyRegion ? 8 : 5; // More failures allowed for high-latency regions
const dbResetTimeout = isHighLatencyRegion ? 60000 : 30000; // Longer reset timeout for high-latency regions

export const databaseCircuitBreaker = new CircuitBreaker('database', dbFailureThreshold, dbResetTimeout);
