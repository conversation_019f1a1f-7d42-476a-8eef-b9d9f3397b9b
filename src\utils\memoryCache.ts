/**
 * Memory Cache Utility
 *
 * This utility provides a fast in-memory LRU cache for frequently accessed data.
 * It's used as a first-level cache before checking Redis to reduce latency.
 */

import logger from './logger';

// Ultra-optimized LRU cache implementation
class LRUCache<T> {
  private capacity: number;
  private cache: Map<string, { value: T; timestamp: number; hits: number }>;
  private highTrafficKeys: Set<string>; // Track high-traffic keys for special handling

  constructor(capacity: number = 500) {
    this.capacity = capacity;
    this.cache = new Map();
    this.highTrafficKeys = new Set();

    // Add common high-traffic keys
    this.highTrafficKeys.add('domains:all');
    this.highTrafficKeys.add('domains:all:public');

    // Periodically clean expired entries
    setInterval(() => this.cleanExpired(), 60000); // Clean every minute
  }

  // Get a value from the cache - optimized for speed
  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    // Fast expiry check
    if (entry.timestamp < Date.now()) {
      this.cache.delete(key);
      return undefined;
    }

    // Only increment hits for non-high-traffic keys to reduce overhead
    if (!this.highTrafficKeys.has(key)) {
      entry.hits++;
    }

    return entry.value;
  }

  // PHASE 3: Optimized set method with proper memory monitoring
  set(key: string, value: T, ttlSeconds: number = 300): void {
    // If this is a high-traffic key, mark it
    if (key.startsWith('domains:') || key.includes(':list:')) {
      this.highTrafficKeys.add(key);
    }

    // PHASE 3: More aggressive capacity management to prevent memory leaks
    if (this.cache.size >= this.capacity) {
      this.evictBatch();
    }

    // Calculate expiry timestamp
    const timestamp = Date.now() + (ttlSeconds * 1000);

    // Store the value
    this.cache.set(key, {
      value,
      timestamp,
      hits: this.highTrafficKeys.has(key) ? 1000 : 0 // Prioritize high-traffic keys
    });

    // PHASE 3: Monitor memory usage and trigger cleanup if needed
    if (this.cache.size > this.capacity * 0.9) {
      // Schedule cleanup in next tick to avoid blocking
      setImmediate(() => this.cleanup());
    }
  }

  // Delete a value from the cache
  delete(key: string): boolean {
    this.highTrafficKeys.delete(key); // Remove from high-traffic set if present
    return this.cache.delete(key);
  }

  // Clear the entire cache
  clear(): void {
    this.cache.clear();
    this.highTrafficKeys.clear();
  }

  // Get the size of the cache
  size(): number {
    return this.cache.size;
  }

  // Clean expired entries
  private cleanExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < now) {
        this.cache.delete(key);
      }
    }
  }

  // PHASE 3: Improved LRU eviction algorithm with proper memory management
  private evictBatch(): void {
    // PHASE 3: More aggressive eviction to prevent memory leaks
    if (this.cache.size <= this.capacity * 0.8) return; // Start evicting at 80% capacity

    // PHASE 3: Clean expired entries first
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < now) {
        expiredKeys.push(key);
      }
    }

    // Remove expired entries
    for (const key of expiredKeys) {
      this.cache.delete(key);
      this.highTrafficKeys.delete(key);
    }

    // If still over capacity, remove LRU items
    if (this.cache.size > this.capacity * 0.8) {
      // Calculate how many items to remove (25% of capacity for more aggressive cleanup)
      const removeCount = Math.ceil(this.capacity * 0.25);

      // Convert to array for sorting, excluding high-traffic keys
      const entries = Array.from(this.cache.entries())
        .filter(([key]) => !this.highTrafficKeys.has(key)) // Don't evict high-traffic keys
        .sort(([, a], [, b]) => a.hits - b.hits); // Sort by hits (ascending)

      // Remove the least used entries
      for (let i = 0; i < Math.min(removeCount, entries.length); i++) {
        this.cache.delete(entries[i][0]);
      }
    }

    // PHASE 3: Log eviction stats in development only
    if (process.env.NODE_ENV !== 'production' && (expiredKeys.length > 0 || this.cache.size > this.capacity * 0.8)) {
      console.debug(`Memory cache cleanup: expired=${expiredKeys.length}, cache size=${this.cache.size}/${this.capacity}`);
    }
  }

  // PHASE 3: Implement the cleanup method that was referenced in the set() method
  cleanup(): void {
    // Clean expired entries first
    this.cleanExpired();

    // Then run eviction if needed
    this.evictBatch();
  }

  // Get cache statistics
  getStats(): { size: number; hitRate: number; avgHits: number } {
    const size = this.cache.size;
    let totalHits = 0;
    let hitCount = 0;

    for (const entry of this.cache.values()) {
      totalHits += entry.hits;
      if (entry.hits > 0) {
        hitCount++;
      }
    }

    const hitRate = size > 0 ? hitCount / size : 0;
    const avgHits = hitCount > 0 ? totalHits / hitCount : 0;

    return { size, hitRate, avgHits };
  }
}

// Create a singleton instance
const memoryCache = new LRUCache<any>(
  parseInt(process.env.MEMORY_CACHE_SIZE || '500', 10)
);

// Ultra-optimized functions for interacting with the cache
// No logging in production for maximum performance
export const getMemoryCache = <T>(key: string): T | undefined => {
  // Fast path for production
  if (process.env.NODE_ENV === 'production') {
    return memoryCache.get(key) as T | undefined;
  }

  // Development path with logging
  const value = memoryCache.get(key);
  if (value !== undefined) {
    logger.debug(`Memory cache hit for key: ${key}`);
  }
  return value as T | undefined;
};

export const setMemoryCache = <T>(key: string, value: T, ttlSeconds?: number): void => {
  // Fast path for production - no logging
  if (process.env.NODE_ENV === 'production') {
    memoryCache.set(key, value, ttlSeconds);
    return;
  }

  // Development path with logging
  memoryCache.set(key, value, ttlSeconds);
  logger.debug(`Set in memory cache for key: ${key} with TTL: ${ttlSeconds || 300}s`);
};

export const deleteMemoryCache = (key: string): boolean => {
  // Fast path for production - no logging
  if (process.env.NODE_ENV === 'production') {
    return memoryCache.delete(key);
  }

  // Development path with logging
  const result = memoryCache.delete(key);
  logger.debug(`Deleted from memory cache for key: ${key}`);
  return result;
};

export const clearMemoryCache = (): void => {
  memoryCache.clear();

  // Only log in development
  if (process.env.NODE_ENV !== 'production') {
    logger.debug('Memory cache cleared');
  }
};

export const getMemoryCacheStats = (): { size: number; hitRate: number; avgHits: number } => {
  return memoryCache.getStats();
};

export default memoryCache;
