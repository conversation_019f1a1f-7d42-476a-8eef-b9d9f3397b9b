/**
 * Error Boundary Utilities
 * 
 * This module provides comprehensive error handling utilities to catch
 * silent failures and ensure proper error logging and response handling.
 */

import { Request, Response, NextFunction } from 'express';
import logger from './logger';
import { v4 as uuidv4 } from 'uuid';

// Error context interface
interface ErrorContext {
  operation: string;
  requestId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

// Enhanced error class with context
export class ContextualError extends Error {
  public readonly context: ErrorContext;
  public readonly timestamp: Date;
  public readonly originalError?: Error;

  constructor(message: string, context: ErrorContext, originalError?: Error) {
    super(message);
    this.name = 'ContextualError';
    this.context = context;
    this.timestamp = new Date();
    this.originalError = originalError;
  }
}

// Async operation wrapper with comprehensive error handling
export const withErrorBoundary = async <T>(
  operation: () => Promise<T>,
  context: ErrorContext
): Promise<T> => {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    
    // Log successful operations in debug mode
    const duration = Date.now() - startTime;
    if (process.env.NODE_ENV !== 'production' && duration > 1000) {
      logger.debug(`Slow operation completed: ${context.operation} took ${duration}ms (requestId: ${context.requestId})`);
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    const err = error instanceof Error ? error : new Error(String(error));
    
    // Create contextual error
    const contextualError = new ContextualError(
      `${context.operation} failed: ${err.message}`,
      context,
      err
    );
    
    // Log the error with full context
    logger.error(`Error in ${context.operation}: ${err.message} (duration: ${duration}ms, requestId: ${context.requestId}, timestamp: ${contextualError.timestamp.toISOString()})`);
    
    // Re-throw to maintain error flow
    throw contextualError;
  }
};

// Express middleware wrapper for error boundaries
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const requestId = (req as any).requestId || uuidv4().toUpperCase();
    
    const context: ErrorContext = {
      operation: `${req.method} ${req.path}`,
      requestId,
      metadata: {
        method: req.method,
        path: req.path,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      }
    };
    
    withErrorBoundary(() => fn(req, res, next), context)
      .catch(next); // Pass errors to Express error handler
  };
};

// Database operation wrapper
export const withDatabaseErrorBoundary = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  requestId?: string
): Promise<T> => {
  return withErrorBoundary(operation, {
    operation: `Database: ${operationName}`,
    requestId,
    metadata: { type: 'database' }
  });
};

// Redis operation wrapper
export const withRedisErrorBoundary = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  requestId?: string
): Promise<T> => {
  return withErrorBoundary(operation, {
    operation: `Redis: ${operationName}`,
    requestId,
    metadata: { type: 'redis' }
  });
};

// External API operation wrapper
export const withExternalApiErrorBoundary = async <T>(
  operation: () => Promise<T>,
  apiName: string,
  requestId?: string
): Promise<T> => {
  return withErrorBoundary(operation, {
    operation: `External API: ${apiName}`,
    requestId,
    metadata: { type: 'external_api' }
  });
};

// Silent failure detector for promises
export const detectSilentFailures = () => {
  // Track unhandled promise rejections
  const unhandledRejections = new Map<Promise<any>, Error>();
  
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    unhandledRejections.set(promise, error);
    
    logger.error(`SILENT FAILURE DETECTED - Unhandled Promise Rejection: ${error.message} (timestamp: ${new Date().toISOString()})`);
    
    // Clean up after 30 seconds
    setTimeout(() => {
      unhandledRejections.delete(promise);
    }, 30000);
  });
  
  process.on('rejectionHandled', (promise: Promise<any>) => {
    if (unhandledRejections.has(promise)) {
      logger.info(`Previously unhandled rejection was handled (timestamp: ${new Date().toISOString()})`);
      unhandledRejections.delete(promise);
    }
  });
  
  // Track uncaught exceptions
  process.on('uncaughtException', (error: Error) => {
    logger.error(`SILENT FAILURE DETECTED - Uncaught Exception: ${error.message} (timestamp: ${new Date().toISOString()})`);
    
    // Give logger time to write before exiting
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });
  
  logger.info('Silent failure detection initialized');
};

// Timeout wrapper that prevents silent hangs
export const withTimeoutErrorBoundary = async <T>(
  operation: () => Promise<T>,
  timeoutMs: number,
  operationName: string,
  requestId?: string
): Promise<T> => {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      const error = new Error(`${operationName} timed out after ${timeoutMs}ms`);
      logger.error(`Operation timeout detected: ${operationName} timed out after ${timeoutMs}ms (requestId: ${requestId}, timestamp: ${new Date().toISOString()})`);
      reject(error);
    }, timeoutMs);
  });
  
  return Promise.race([
    withErrorBoundary(operation, {
      operation: operationName,
      requestId,
      metadata: { timeout: timeoutMs }
    }),
    timeoutPromise
  ]);
};

export default {
  withErrorBoundary,
  asyncHandler,
  withDatabaseErrorBoundary,
  withRedisErrorBoundary,
  withExternalApiErrorBoundary,
  detectSilentFailures,
  withTimeoutErrorBoundary,
  ContextualError
};
