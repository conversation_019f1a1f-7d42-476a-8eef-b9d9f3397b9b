import pool from '../config/database';
import dotenv from 'dotenv';
import logger from './logger';

// Load environment variables
dotenv.config();

// Initial domains to seed
const initialDomains = [
  'umorjjsassaas.store',
  'umorjjsdsd.site',
  'trendygadgetshop.store',
  'wellnesswavecenter.site',
  'fitnessfocusgym.site',
  'joibcddsd.online',
  'greenthumbgardens.store',
  'gourmetguidecafe.site',
  'rubymail.site',
  'probox.site',
  'mailvu.space',
  'boxqix.com'
];

/**
 * Seed initial domains
 */
async function seedDomains() {
  try {
    logger.info('Seeding initial domains...');

    // Check if domains table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'domains'
      );
    `);

    if (!tableCheck.rows[0].exists) {
      logger.error('Domains table does not exist. Run migrations first.');
      process.exit(1);
    }

    // Check if domains already exist
    const existingDomains = await pool.query('SELECT domain FROM domains');
    const existingDomainNames = existingDomains.rows.map((row) => row.domain);

    // Filter out domains that already exist
    const domainsToAdd = initialDomains.filter(
      (domain) => !existingDomainNames.includes(domain)
    );

    if (domainsToAdd.length === 0) {
      logger.info('All domains already exist. No new domains added.');
      return;
    }

    // Insert new domains
    for (const domain of domainsToAdd) {
      await pool.query('INSERT INTO domains (domain) VALUES ($1)', [domain]);
      logger.info(`Added domain: ${domain}`);
    }

    logger.info(`Successfully added ${domainsToAdd.length} domains.`);
  } catch (error) {
    logger.error('Error seeding domains:', error as Error);
  } finally {
    // Close the pool using proper shutdown procedure
    const { shutdownDatabasePools } = await import('../config/database-pools');
    await shutdownDatabasePools();
  }
}

// Run the seed function
seedDomains();
