import pool from '../config/database';
import logger from '../utils/logger';

export interface RapidApiUsageData {
  user: string;
  requestId?: string;
  rateLimitLimit: number;
  rateLimitRemaining: number;
  rateLimitReset: number;
  planType: string;
  region?: string;
  version?: string;
  endpoint: string;
  method: string;
}

export interface RapidApiUsageSummary {
  user: string;
  totalRequests: number;
  lastRequest: Date;
  planType: string;
  rateLimitLimit: number;
  rateLimitRemaining: number;
  rateLimitReset: number;
}

export class RapidApiUsageModel {
  /**
   * Track RapidAPI usage
   */
  static async trackUsage(data: RapidApiUsageData): Promise<void> {
    try {
      const query = `
        INSERT INTO rapidapi_usage (
          user_id, request_id, rate_limit_limit, rate_limit_remaining,
          rate_limit_reset, plan_type, region, version, endpoint, method
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `;

      const values = [
        data.user,
        data.requestId || null,
        data.rateLimitLimit,
        data.rateLimitRemaining,
        data.rateLimitReset,
        data.planType,
        data.region || null,
        data.version || null,
        data.endpoint,
        data.method
      ];

      await pool.query(query, values);
    } catch (error) {
      // Log error but don't fail the operation
      logger.error('Error storing RapidAPI usage data:', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Get usage summary for a specific RapidAPI user
   */
  static async getUserSummary(user: string): Promise<RapidApiUsageSummary | null> {
    try {
      const query = `
        SELECT
          user_id as user,
          COUNT(*) as total_requests,
          MAX(created_at) as last_request,
          plan_type,
          rate_limit_limit,
          rate_limit_remaining,
          rate_limit_reset
        FROM rapidapi_usage
        WHERE user_id = $1
        GROUP BY user_id, plan_type, rate_limit_limit, rate_limit_remaining, rate_limit_reset
        ORDER BY last_request DESC
        LIMIT 1
      `;

      const result = await pool.query(query, [user]);
      return result.rows.length ? result.rows[0] : null;
    } catch (error) {
      logger.error('Error getting RapidAPI user summary:', error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * Get usage statistics for all RapidAPI users
   */
  static async getAllUserStats(limit: number = 100): Promise<any[]> {
    try {
      const query = `
        SELECT
          user_id as user,
          COUNT(*) as request_count,
          MAX(created_at) as last_request,
          plan_type,
          MAX(rate_limit_limit) as rate_limit_limit
        FROM rapidapi_usage
        GROUP BY user_id, plan_type
        ORDER BY request_count DESC
        LIMIT $1
      `;

      const result = await pool.query(query, [limit]);
      return result.rows;
    } catch (error) {
      logger.error('Error getting RapidAPI usage statistics:', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  /**
   * Get usage by endpoint
   */
  static async getEndpointStats(): Promise<any[]> {
    try {
      const query = `
        SELECT
          endpoint,
          method,
          COUNT(*) as request_count
        FROM rapidapi_usage
        GROUP BY endpoint, method
        ORDER BY request_count DESC
      `;

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error getting RapidAPI endpoint statistics:', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  /**
   * Get usage by plan type
   */
  static async getPlanStats(): Promise<any[]> {
    try {
      const query = `
        SELECT
          plan_type,
          COUNT(*) as request_count,
          COUNT(DISTINCT user_id) as user_count
        FROM rapidapi_usage
        GROUP BY plan_type
        ORDER BY request_count DESC
      `;

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error getting RapidAPI plan statistics:', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }

  /**
   * Get recent usage (last 24 hours)
   */
  static async getRecentUsage(): Promise<any[]> {
    try {
      const query = `
        SELECT
          DATE_TRUNC('hour', created_at) as hour,
          COUNT(*) as request_count
        FROM rapidapi_usage
        WHERE created_at > NOW() - INTERVAL '24 hours'
        GROUP BY hour
        ORDER BY hour
      `;

      const result = await pool.query(query);
      return result.rows;
    } catch (error) {
      logger.error('Error getting recent RapidAPI usage:', error instanceof Error ? error : new Error(String(error)));
      return [];
    }
  }
}
