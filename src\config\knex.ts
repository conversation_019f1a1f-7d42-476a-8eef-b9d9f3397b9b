import knex from 'knex';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Check if we're likely connecting to PgBouncer
const isPgBouncer = process.env.PGPORT === '6432' || process.env.USE_PGBOUNCER === 'true';

// Create connection configuration
const connection = process.env.DATABASE_URL || {
  host: process.env.PGHOST,
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  ssl: process.env.PGSSLMODE === 'disable' ? false : { rejectUnauthorized: false },
};

// Create pool configuration optimized for remote PostgreSQL
const poolConfig = {
  min: isPgBouncer ? 1 : 3,
  max: isPgBouncer ? 5 : 12,
  idleTimeoutMillis: isPgBouncer ? 10000 : 60000,
};

// Create knex instance
const db = knex({
  client: 'pg',
  connection,
  pool: poolConfig,
});

export default db;
