/**
 * Prepared Statements Configuration
 *
 * This module sets up prepared statements for frequently used database queries
 * to improve performance by reducing query parsing overhead.
 */

import pool from './database';
import logger from '../utils/logger';

// Define prepared statements with their names and SQL
const PREPARED_STATEMENTS = {
  // Inbox queries
  GET_INBOX_BY_ID: {
    name: 'get_inbox_by_id',
    text: 'SELECT * FROM inboxes WHERE id = $1 AND is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())'
  },
  GET_INBOX_BY_EMAIL: {
    name: 'get_inbox_by_email',
    text: 'SELECT * FROM inboxes WHERE address = $1 AND is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())'
  },
  COUNT_INBOXES_BY_RAPIDAPI_KEY: {
    name: 'count_inboxes_by_rapidapi_key',
    text: 'SELECT COUNT(*) FROM inboxes WHERE rapidapi_key = $1 AND is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())'
  },
  CHECK_INBOX_OWNERSHIP_API_KEY: {
    name: 'check_inbox_ownership_api_key',
    text: 'SELECT id FROM inboxes WHERE id = $1 AND api_key_id = $2 AND is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())'
  },
  CHECK_INBOX_OWNERSHIP_RAPIDAPI_KEY: {
    name: 'check_inbox_ownership_rapidapi_key',
    text: 'SELECT id FROM inboxes WHERE id = $1 AND rapidapi_key = $2 AND is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())'
  },

  // Email queries
  GET_EMAIL_BY_ID: {
    name: 'get_email_by_id',
    text: 'SELECT * FROM emails WHERE id = $1 AND inbox_id = $2'
  },
  GET_ATTACHMENTS_BY_EMAIL_ID: {
    name: 'get_attachments_by_email_id',
    text: 'SELECT id, filename, content_type, size, content_id, is_inline, created_at FROM attachments WHERE email_id = $1'
  },
  MARK_EMAIL_AS_READ: {
    name: 'mark_email_as_read',
    text: 'UPDATE emails SET read_at = NOW(), updated_at = NOW() WHERE id = $1 AND inbox_id = $2 RETURNING read_at'
  },

  // Domain queries
  GET_ALL_DOMAINS: {
    name: 'get_all_domains',
    text: 'SELECT domain, is_active, created_at, updated_at FROM domains WHERE is_active = true ORDER BY domain ASC'
  }
};

/**
 * Always use prepared statements since we're not using PgBouncer anymore
 */
async function isUsingPgBouncer(): Promise<boolean> {
  // Always return false since we're not using PgBouncer anymore
  return false;
}

// Store whether we're using PgBouncer (always false)
let usingPgBouncer = false;

/**
 * Initialize prepared statements
 */
export async function initPreparedStatements(): Promise<void> {
  try {
    logger.info('Initializing prepared statements...');

    // Always use prepared statements
    usingPgBouncer = false;

    // Get a client from the pool
    const client = await pool.connect();

    try {
      // Prepare each statement
      for (const [key, statement] of Object.entries(PREPARED_STATEMENTS)) {
        try {
          await client.query(`PREPARE ${statement.name} AS ${statement.text}`);
          logger.info(`Prepared statement ${statement.name} created successfully`);
        } catch (error) {
          logger.error(`Failed to prepare statement ${statement.name}:`, error instanceof Error ? error : new Error(String(error)));
        }
      }

      logger.info('All prepared statements initialized');
    } finally {
      // Release the client back to the pool
      client.release();
    }
  } catch (error) {
    logger.error('Failed to initialize prepared statements:', error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Execute a prepared statement
 * @param name The name of the prepared statement
 * @param params The parameters to pass to the prepared statement
 * @returns The query result
 */
export async function executePrepared(name: string, params: any[] = []): Promise<any> {
  try {
    // Get the statement definition
    const statement = Object.values(PREPARED_STATEMENTS).find(s => s.name === name);

    if (!statement) {
      throw new Error(`Prepared statement ${name} not found`);
    }

    // Get a client from the pool
    const client = await pool.connect();

    try {
      // Execute the prepared statement
      const result = await client.query(`EXECUTE ${name}(${params.map((_, i) => `$${i + 1}`).join(', ')})`, params);
      return result;
    } finally {
      // Release the client back to the pool
      client.release();
    }
  } catch (error) {
    logger.error(`Failed to execute prepared statement ${name}:`, error instanceof Error ? error : new Error(String(error)));
    throw error;
  }
}

export default {
  initPreparedStatements,
  executePrepared,
  PREPARED_STATEMENTS
};
