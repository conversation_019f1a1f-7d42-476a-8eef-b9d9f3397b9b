import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

/**
 * Middleware to completely disable caching for specific routes
 * 
 * This middleware:
 * 1. Sets HTTP headers to prevent browser caching
 * 2. Adds a query parameter to skip the cache middleware
 * 3. Logs that caching is disabled for the route
 */
export const noCacheMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Set HTTP headers to prevent browser caching
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('Surrogate-Control', 'no-store');
  
  // Add a custom header to indicate caching is disabled
  res.setHeader('X-Cache-Disabled', 'true');
  
  // Add a query parameter to skip the cache middleware
  // This ensures that the cacheMiddleware will not cache this request
  (req.query as any).skipCache = 'true';
  
  // Add a custom header to skip cache in case query params are not checked
  req.headers['x-skip-cache'] = 'true';
  
  // Log that caching is disabled for this route (only in development)
  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Caching disabled for route: ${req.method} ${req.originalUrl}`);
  }
  
  next();
};
