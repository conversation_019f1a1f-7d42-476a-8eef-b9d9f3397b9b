/**
 * Unified Timeout Configuration
 * 
 * This module provides centralized timeout management to prevent conflicts
 * between different timeout layers and ensure consistent behavior across regions.
 */

import * as dotenv from 'dotenv';
import logger from '../utils/logger';

// Load environment variables
dotenv.config();

// Region detection for timeout adjustments
const instanceId = process.env.INSTANCE_ID || 'unknown';
const region = process.env.REGION || 'unknown';

// Determine if this is a high-latency region
const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                            region.toLowerCase().includes('in') ||
                            instanceId.toLowerCase().includes('india') ||
                            instanceId.toLowerCase().includes('in');

// Base timeout multipliers by region
const getTimeoutMultiplier = (): number => {
  if (isHighLatencyRegion) {
    return 2.5; // India and high-latency regions
  } else {
    return 1.0; // US, EU and low-latency regions
  }
};

const timeoutMultiplier = getTimeoutMultiplier();

// Base timeout values (in milliseconds)
const BASE_TIMEOUTS = {
  // HTTP Request timeouts
  REQUEST_TIMEOUT: 15000,        // 15 seconds for HTTP requests
  REQUEST_TIMEOUT_LONG: 25000,   // 25 seconds for long operations (POST, etc.)
  
  // Database timeouts
  DB_CONNECTION: 10000,          // 10 seconds to establish connection
  DB_ACQUIRE: 5000,              // 5 seconds to acquire from pool
  DB_QUERY: 30000,               // 30 seconds for query execution
  DB_STATEMENT: 45000,           // 45 seconds for statement execution
  DB_HEALTH_CHECK: 3000,         // 3 seconds for health checks
  
  // Redis timeouts
  REDIS_CONNECT: 5000,           // 5 seconds to connect
  REDIS_COMMAND: 3000,           // 3 seconds for commands
  REDIS_GET: 2000,               // 2 seconds for get operations
  REDIS_SET: 3000,               // 3 seconds for set operations
  
  // Cache and warmup timeouts
  CACHE_WARMUP: 30000,           // 30 seconds for cache warmup
  DOMAIN_PRELOAD: 20000,         // 20 seconds for domain preloading
  
  // Health check timeouts
  HEALTH_CHECK_QUICK: 2000,      // 2 seconds for quick health checks
  HEALTH_CHECK_FULL: 5000,       // 5 seconds for full health checks
  
  // Inbox operations
  INBOX_CREATION: 8000,          // 8 seconds for inbox creation
  EMAIL_FETCH: 10000,            // 10 seconds for email fetching
};

// Apply regional multipliers to create final timeout configuration
export const TIMEOUTS = {
  // HTTP Request timeouts
  REQUEST_TIMEOUT: Math.round(BASE_TIMEOUTS.REQUEST_TIMEOUT * timeoutMultiplier),
  REQUEST_TIMEOUT_LONG: Math.round(BASE_TIMEOUTS.REQUEST_TIMEOUT_LONG * timeoutMultiplier),
  
  // Database timeouts
  DB_CONNECTION: Math.round(BASE_TIMEOUTS.DB_CONNECTION * timeoutMultiplier),
  DB_ACQUIRE: Math.round(BASE_TIMEOUTS.DB_ACQUIRE * timeoutMultiplier),
  DB_QUERY: Math.round(BASE_TIMEOUTS.DB_QUERY * timeoutMultiplier),
  DB_STATEMENT: Math.round(BASE_TIMEOUTS.DB_STATEMENT * timeoutMultiplier),
  DB_HEALTH_CHECK: Math.round(BASE_TIMEOUTS.DB_HEALTH_CHECK * timeoutMultiplier),
  
  // Redis timeouts (less affected by region)
  REDIS_CONNECT: Math.round(BASE_TIMEOUTS.REDIS_CONNECT * Math.min(timeoutMultiplier, 1.5)),
  REDIS_COMMAND: Math.round(BASE_TIMEOUTS.REDIS_COMMAND * Math.min(timeoutMultiplier, 1.5)),
  REDIS_GET: Math.round(BASE_TIMEOUTS.REDIS_GET * Math.min(timeoutMultiplier, 1.5)),
  REDIS_SET: Math.round(BASE_TIMEOUTS.REDIS_SET * Math.min(timeoutMultiplier, 1.5)),
  
  // Cache and warmup timeouts
  CACHE_WARMUP: Math.round(BASE_TIMEOUTS.CACHE_WARMUP * timeoutMultiplier),
  DOMAIN_PRELOAD: Math.round(BASE_TIMEOUTS.DOMAIN_PRELOAD * timeoutMultiplier),
  
  // Health check timeouts
  HEALTH_CHECK_QUICK: Math.round(BASE_TIMEOUTS.HEALTH_CHECK_QUICK * timeoutMultiplier),
  HEALTH_CHECK_FULL: Math.round(BASE_TIMEOUTS.HEALTH_CHECK_FULL * timeoutMultiplier),
  
  // Inbox operations
  INBOX_CREATION: Math.round(BASE_TIMEOUTS.INBOX_CREATION * timeoutMultiplier),
  EMAIL_FETCH: Math.round(BASE_TIMEOUTS.EMAIL_FETCH * timeoutMultiplier),
};

// Timeout configuration info
export const TIMEOUT_CONFIG = {
  region,
  instanceId,
  isHighLatencyRegion,
  timeoutMultiplier,
  baseTimeouts: BASE_TIMEOUTS,
  finalTimeouts: TIMEOUTS,
};

// Helper function to create timeout promises
export const createTimeoutPromise = <T>(timeoutMs: number, errorMessage?: string): Promise<T> => {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(errorMessage || `Operation timeout after ${timeoutMs}ms`));
    }, timeoutMs);
  });
};

// Helper function to race an operation against a timeout
export const withTimeout = async <T>(
  operation: Promise<T>,
  timeoutMs: number,
  errorMessage?: string
): Promise<T> => {
  return Promise.race([
    operation,
    createTimeoutPromise<T>(timeoutMs, errorMessage)
  ]);
};

// Log timeout configuration on startup
logger.info(`Timeout configuration initialized for region: ${region} (instance: ${instanceId}, multiplier: ${timeoutMultiplier}, request: ${TIMEOUTS.REQUEST_TIMEOUT}ms, db: ${TIMEOUTS.DB_CONNECTION}ms, redis: ${TIMEOUTS.REDIS_CONNECT}ms)`);

export default TIMEOUTS;
