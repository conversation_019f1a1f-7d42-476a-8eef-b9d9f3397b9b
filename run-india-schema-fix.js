#!/usr/bin/env node

/**
 * CRITICAL DATABASE SCHEMA FIX FOR INDIA REGION
 * 
 * This script connects to the India PostgreSQL database and runs the schema migration
 * to fix missing tables and columns causing dashboard API failures.
 * 
 * Usage: node run-india-schema-fix.js
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// India database connection configuration
const dbConfig = {
  host: 'host.docker.internal', // India local database
  port: 5432,
  database: 'tempfly_app',
  user: 'postgres',
  password: '4wyWCAAk92hkGUhdh7',
  ssl: false
};

async function runSchemaMigration() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 Connecting to India PostgreSQL database...');
    await client.connect();
    console.log('✅ Connected to database successfully');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'fix-india-schema-migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('🔄 Running schema migration...');
    console.log('📝 Migration includes:');
    console.log('   - Adding deleted_at, region, last_accessed_at columns to inboxes table');
    console.log('   - Adding deleted_at, subscription_tier columns to api_keys table');
    console.log('   - Adding deleted_at column to emails table');
    console.log('   - Creating request_logs table for dashboard statistics');
    console.log('   - Creating security_logs table for security metrics');
    console.log('   - Creating maintenance_logs table for audit logging');
    console.log('   - Adding performance indexes');

    // Execute the migration
    const result = await client.query(migrationSQL);
    
    console.log('✅ Schema migration completed successfully!');
    
    // Run verification queries
    console.log('\n🔍 Running verification checks...');
    
    // Check inboxes table columns
    const inboxesCheck = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'inboxes' 
      AND column_name IN ('deleted_at', 'region', 'last_accessed_at')
      ORDER BY column_name
    `);
    
    console.log('\n📋 Inboxes table columns:');
    inboxesCheck.rows.forEach(row => {
      console.log(`   ✓ ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Check api_keys table columns
    const apiKeysCheck = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'api_keys' 
      AND column_name IN ('deleted_at', 'subscription_tier')
      ORDER BY column_name
    `);
    
    console.log('\n📋 API Keys table columns:');
    apiKeysCheck.rows.forEach(row => {
      console.log(`   ✓ ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

    // Check request_logs table
    const requestLogsCheck = await client.query(`
      SELECT COUNT(*) as column_count
      FROM information_schema.columns 
      WHERE table_name = 'request_logs'
    `);
    
    console.log(`\n📋 Request logs table: ${requestLogsCheck.rows[0].column_count} columns created`);

    // Check security_logs table
    const securityLogsCheck = await client.query(`
      SELECT COUNT(*) as column_count
      FROM information_schema.columns 
      WHERE table_name = 'security_logs'
    `);
    
    console.log(`📋 Security logs table: ${securityLogsCheck.rows[0].column_count} columns created`);

    // List all tables
    const tablesCheck = await client.query(`
      SELECT tablename
      FROM pg_tables 
      WHERE schemaname = 'public' 
      ORDER BY tablename
    `);
    
    console.log('\n📋 All tables in database:');
    tablesCheck.rows.forEach(row => {
      console.log(`   • ${row.tablename}`);
    });

    console.log('\n🎉 India database schema migration completed successfully!');
    console.log('🚀 The dashboard API endpoints should now work correctly.');
    console.log('💡 You may need to restart the India application container for changes to take effect.');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Ensure PostgreSQL is running on the India server');
    console.error('   2. Verify database connection credentials');
    console.error('   3. Check if the database user has sufficient privileges');
    console.error('   4. Ensure the tempfly_app database exists');
    
    if (error.code) {
      console.error(`   Error Code: ${error.code}`);
    }
    
    process.exit(1);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the migration
console.log('🚀 Starting India Database Schema Migration');
console.log('=' .repeat(50));
runSchemaMigration();
