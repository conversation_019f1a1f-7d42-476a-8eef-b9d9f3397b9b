import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { deleteCache, flushCache, isRedisConnected, getCacheStats } from '../config/redis-direct';
import os from 'os';
import pool from '../config/database';
import { checkDatabaseHealth, getDatabaseConfig } from '../utils/databaseRouter';
import { getWritePoolHealth, getReadPoolHealth } from '../config/database-pools';
import logger from '../utils/logger';
import { RapidApiUsageModel } from '../models/RapidApiUsage';

/**
 * Health check endpoint
 */
export const healthCheck = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const requestId = req.headers['x-request-id'] || uuidv4();

    // Check database connections (read/write splitting aware)
    let dbStatus = 'ok';
    let dbDetails = {};
    try {
      const healthCheck = await checkDatabaseHealth();
      const dbConfig = getDatabaseConfig();

      dbStatus = (healthCheck.write.healthy && healthCheck.read.healthy) ? 'ok' : 'degraded';

      dbDetails = {
        write: {
          healthy: healthCheck.write.healthy,
          latency: healthCheck.write.latency,
          error: healthCheck.write.error
        },
        read: {
          healthy: healthCheck.read.healthy,
          latency: healthCheck.read.latency,
          error: healthCheck.read.error
        },
        configuration: {
          hasReadWriteSplitting: dbConfig.hasReadWriteSplitting,
          writeDatabase: `${dbConfig.writeConfig.host}:${dbConfig.writeConfig.port}`,
          readDatabase: dbConfig.readConfig ? `${dbConfig.readConfig.host}:${dbConfig.readConfig.port}` : 'Same as write'
        }
      };
    } catch (error) {
      dbStatus = 'error';
      dbDetails = { error: error instanceof Error ? error.message : String(error) };
    }

    // Check Redis connection
    const redisStatus = isRedisConnected() ? 'ok' : 'error';

    // Get system info - detailed in development, limited in production
    let systemInfo = {};

    if (process.env.NODE_ENV !== 'production') {
      // Detailed system info for development environments
      systemInfo = {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: os.cpus(),
        loadavg: os.loadavg(),
        platform: process.platform,
        nodeVersion: process.version
      };
    } else {
      // Limited system info for production
      systemInfo = {
        uptime: process.uptime()
      };
    }

    const response = {
      'request-id': requestId,
      'message': 'Service is healthy',
      'data': {
        'status': 'ok',
        'version': process.env.npm_package_version || '1.0.0',
        'environment': process.env.NODE_ENV || 'development',
        'database': dbStatus,
        ...(process.env.NODE_ENV !== 'production' ? { 'database_details': dbDetails } : {}),
        'redis': redisStatus,
        ...(process.env.NODE_ENV !== 'production' ? { 'system': systemInfo } : {})
      }
    };

    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Metrics endpoint
 */
export const getMetrics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const requestId = req.headers['x-request-id'] || uuidv4();

    // Check if user has admin privileges
    const isAdmin = req.apiKey?.name?.toLowerCase().includes('admin') || false;

    // Get database metrics - only if admin or not in production
    let dbMetrics = {};
    if (isAdmin || process.env.NODE_ENV !== 'production') {
      try {
        const dbResult = await pool.query(`
          SELECT
            (SELECT COUNT(*) FROM inboxes) AS inbox_count,
            (SELECT COUNT(*) FROM emails) AS email_count,
            (SELECT COUNT(*) FROM inboxes WHERE is_active = true) AS active_inbox_count,
            (SELECT COUNT(*) FROM emails WHERE has_attachments = true) AS emails_with_attachments
        `);
        dbMetrics = dbResult.rows[0];
      } catch (error) {
        logger.error('Error getting database metrics:', error as Error);
      }
    } else {
      // Limited metrics for non-admin users in production
      dbMetrics = { status: 'available' };
    }

    // Get system metrics - detailed for admins/dev, limited for others in production
    let systemMetrics = {};

    if (isAdmin || process.env.NODE_ENV !== 'production') {
      systemMetrics = {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu_usage: os.loadavg()[0], // 1 minute load average
        free_memory: os.freemem(),
        total_memory: os.totalmem()
      };
    } else {
      // Limited metrics for non-admin users in production
      systemMetrics = {
        uptime: process.uptime()
      };
    }

    // Get Redis metrics for all users
    let redisMetrics: any = {
      'connected': isRedisConnected()
    };

    if (isRedisConnected()) {
      try {
        const stats = await getCacheStats();
        redisMetrics.stats = stats;
      } catch (error) {
        logger.error('Error getting Redis stats:', error as Error);
      }
    }

    // Get RapidAPI metrics if admin or in development
    let rapidApiMetrics = {};
    if (isAdmin || process.env.NODE_ENV !== 'production') {
      try {
        // Get RapidAPI usage statistics
        const planStats = await RapidApiUsageModel.getPlanStats();
        const endpointStats = await RapidApiUsageModel.getEndpointStats();
        const recentUsage = await RapidApiUsageModel.getRecentUsage();
        const topUsers = await RapidApiUsageModel.getAllUserStats(10); // Top 10 users

        rapidApiMetrics = {
          plans: planStats,
          topEndpoints: endpointStats.slice(0, 10), // Top 10 endpoints
          recentUsage,
          topUsers
        };
      } catch (error) {
        logger.error('Error getting RapidAPI metrics:', error as Error);
      }
    }

    const response = {
      'request-id': requestId,
      'message': 'Success',
      'data': {
        'database': dbMetrics,
        'system': systemMetrics,
        'redis': redisMetrics,
        'rapidapi': rapidApiMetrics
      }
    };

    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Clear cache endpoint
 */
export const clearCache = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const requestId = req.headers['x-request-id'] || uuidv4();

    // Check if user has admin privileges
    const isAdmin = req.apiKey?.name?.toLowerCase().includes('admin') || false;

    // In production, only allow admin users to clear cache
    if (process.env.NODE_ENV === 'production' && !isAdmin) {
      res.status(403).json({
        'request-id': requestId,
        'message': 'Access denied. Admin privileges required to clear cache in production.',
        'data': null,
        'code': 403
      });
      return;
    }

    if (!isRedisConnected()) {
      res.status(503).json({
        'request-id': requestId,
        'message': 'Redis is not connected',
        'data': null,
        'code': 503
      });
      return;
    }

    // Flush all cache
    const result = await flushCache();

    const response = {
      'request-id': requestId,
      'message': result ? 'Cache cleared successfully' : 'Failed to clear cache',
      'data': {
        'success': result
      },
      'code': result ? 0 : 500
    };

    res.status(result ? 200 : 500).json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Database health endpoint - detailed database connection status
 */
export const getDatabaseHealth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const requestId = req.headers['x-request-id'] || uuidv4();

    // Check if user has admin privileges
    const isAdmin = req.apiKey?.name?.toLowerCase().includes('admin') || false;

    // In production, only allow admin users to see detailed database health
    if (process.env.NODE_ENV === 'production' && !isAdmin) {
      res.status(403).json({
        'request-id': requestId,
        'message': 'Access denied. Admin privileges required to view detailed database health in production.',
        'data': null,
        'code': 403
      });
      return;
    }

    // Get comprehensive database health information
    const healthCheck = await checkDatabaseHealth();
    const dbConfig = getDatabaseConfig();
    const writePoolHealth = getWritePoolHealth();
    const readPoolHealth = getReadPoolHealth();

    const response = {
      'request-id': requestId,
      'message': 'Database health information',
      'data': {
        'overall_status': (healthCheck.write.healthy && healthCheck.read.healthy) ? 'healthy' : 'degraded',
        'configuration': {
          'read_write_splitting_enabled': dbConfig.hasReadWriteSplitting,
          'write_database': {
            'host': dbConfig.writeConfig.host,
            'port': dbConfig.writeConfig.port,
            'database': dbConfig.writeConfig.database
          },
          'read_database': dbConfig.readConfig ? {
            'host': dbConfig.readConfig.host,
            'port': dbConfig.readConfig.port,
            'database': dbConfig.readConfig.database
          } : 'Same as write database'
        },
        'connection_health': {
          'write_pool': {
            'healthy': healthCheck.write.healthy,
            'latency_ms': healthCheck.write.latency,
            'error': healthCheck.write.error,
            'pool_status': {
              'is_healthy': writePoolHealth.isHealthy,
              'last_check': writePoolHealth.lastCheck,
              'consecutive_failures': writePoolHealth.consecutiveFailures
            }
          },
          'read_pool': {
            'healthy': healthCheck.read.healthy,
            'latency_ms': healthCheck.read.latency,
            'error': healthCheck.read.error,
            'pool_status': dbConfig.hasReadWriteSplitting ? {
              'is_healthy': readPoolHealth.isHealthy,
              'last_check': readPoolHealth.lastCheck,
              'consecutive_failures': readPoolHealth.consecutiveFailures
            } : 'Same as write pool'
          }
        },
        'failover_info': {
          'read_failover_enabled': dbConfig.hasReadWriteSplitting,
          'read_failover_status': dbConfig.hasReadWriteSplitting ?
            (readPoolHealth.isHealthy ? 'not_needed' : 'active') : 'not_applicable'
        }
      },
      'code': 0
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Error getting database health:', error as Error);
    next(error);
  }
};
