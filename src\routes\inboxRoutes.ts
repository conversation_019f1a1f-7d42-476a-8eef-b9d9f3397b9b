import express from 'express';
import * as inboxController from '../controllers/inboxController';
import { validate, validateParams, validateQuery } from '../middlewares/validator';
import { createInboxSchema, inboxIdSchema, listInboxesSchema } from '../validators/inboxValidators';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { rapidApiRateLimiter } from '../middlewares/rapidApiRateLimiter';
import { subscriptionRateLimiter } from '../middlewares/subscriptionRateLimiter';
import { inboxOwnershipAuth } from '../middlewares/inboxOwnershipAuth';
import { freeUserInboxLimiter } from '../middlewares/freeUserInboxLimiter';
import { cacheMiddleware } from '../middlewares/cacheMiddleware';
import { performanceMiddleware } from '../middlewares/performanceOptimizer';
import emailRoutes from './emailRoutes';
// import forwardingRoutes from './forwardingRoutes'; // Forwarding functionality disabled for now
import logger from '../utils/logger';

const router = express.Router();

// Apply authentication first, then subscription-aware rate limiting
router.use(rapidApiAuth);

// Use subscription-aware rate limiter that handles both RapidAPI and regular API key users
// This replaces the separate rapidApiRateLimiter and generalRateLimiter
router.use(subscriptionRateLimiter);

// POST /api/inboxes - Create a new inbox
// PHASE 2: Reduced performance middleware overhead for inbox creation
router.post('/',
  // PHASE 2: Removed performanceMiddleware array to reduce overhead
  validate(createInboxSchema),
  freeUserInboxLimiter,
  inboxController.createInbox
);

// GET /api/inboxes - List all inboxes (no caching to prevent inconsistent results)
// PHASE 2: Optimized for high-traffic - removed caching to fix issue with alternating empty/populated inbox lists
router.get('/', validateQuery(listInboxesSchema), inboxController.listInboxes);

// DELETE /api/inboxes/:id - Delete an inbox
// Requires authentication (RapidAPI or API key) and ownership verification
// The rapidApiAuth middleware is already applied at the router level, so we don't need to apply it again
router.delete('/:id',
  validateParams(inboxIdSchema),
  inboxOwnershipAuth,
  inboxController.deleteInbox
);

// Mount email routes at /api/inboxes/:inboxId/messages
router.use('/:inboxId/messages', (req, res, next) => {
  // Log the inboxId parameter for debugging only in development
  if (process.env.NODE_ENV !== 'production') {
    logger.debug('Mounting email routes with inboxId:', req.params.inboxId);
  }
  next();
}, validateParams(inboxIdSchema), emailRoutes);

// Mount forwarding routes at /api/inboxes/:id/forwarding
// Forwarding functionality disabled for now
// router.use('/:id/forwarding', validateParams(inboxIdSchema), forwardingRoutes);

export default router;
