# Dependency directories
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Build output
dist/
build/
out/
.next/

# Environment variables
.env
.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Log rotation files
logs/*.log.*
*.log.gz
*.log.[0-9]*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# OS files
.DS_Store
Thumbs.db
.directory
Desktop.ini

# Temporary files
*.tmp
*.temp
.cache/
temp/
tmp/

# API keys and secrets
*.pem
*.key
*.cert

# Database files
*.sqlite
*.sqlite3
*.db
Database.md

# Generated files
generated/

# Local development files
.vercel
.netlify

# Redis dump file
dump.rdb

# Docker files
Dockerfile
docker-compose.yml
.dockerignore
nginx.conf
ssl/

# Git files
.git
.gitignore

# Test Scripts
test-scripts/
scripts/

# Documentation
docs/
*.md
!README.md
