/**
 * Direct Redis Client
 *
 * This module provides direct connection to Redis instead of using the Redis API.
 * It implements the same interface as redis-api.ts for compatibility.
 * Enhanced with circuit breaker pattern for improved resilience.
 */

import { createClient } from 'redis';
import dotenv from 'dotenv';
import logger from '../utils/logger';
import { redisCircuitBreaker } from '../utils/circuitBreaker';

// Load environment variables
dotenv.config();

// Default TTL in seconds - increased for better performance
const DEFAULT_TTL = parseInt(process.env.REDIS_DEFAULT_TTL || '7200', 10); // 2 hours default

// Track Redis connection status
let redisConnected = false;
let consecutiveErrors = 0;
const MAX_CONSECUTIVE_ERRORS = 5;

// Limit concurrent Redis operations to prevent connection overload
let currentRedisOps = 0;
const MAX_CONCURRENT_REDIS_OPS = 10; // Increased from 5 to handle more concurrent operations

// PHASE 4: Create Redis client with connection pooling and optimized configuration
const redisClient = createClient({
  url: `redis://:${process.env.REDIS_PASSWORD}@${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
  socket: {
    reconnectStrategy: (retries: number) => {
      // More aggressive exponential backoff with max delay of 3 seconds
      const delay = Math.min(Math.pow(1.3, retries) * 100, 3000);
      logger.debug(`Redis reconnecting in ${delay}ms (attempt ${retries})`);

      // Allow unlimited retries
      return delay;
    },
    // PHASE 4: Optimize socket settings for better performance
    connectTimeout: 5000, // 5 second connection timeout
    keepAlive: 30000, // Increased to 30 seconds to maintain connections longer
    noDelay: true, // Disable Nagle algorithm for better performance
    tls: false, // Disable TLS for better performance (if your Redis server doesn't require it)
  },
  // Optimized Redis settings to reduce cold start latency
  pingInterval: 60000, // More frequent pings to keep connection alive (every minute)
  commandsQueueMaxLength: 5000, // Increased queue length for better stability during bursts
  isolationPoolOptions: {
    // Connection pool settings optimized for reducing cold start
    min: 3, // Increased minimum connections to reduce cold start
    max: 15, // Increased maximum connections for better handling of traffic spikes
    acquireTimeoutMillis: 5000, // Increased timeout for more reliable connections
    idleTimeoutMillis: 60000, // Increased idle timeout to maintain connections longer
    softIdleTimeoutMillis: 30000, // Increased soft idle timeout
    evictionRunIntervalMillis: 300000, // Less frequent eviction runs (every 5 minutes)
    numTestsPerEvictionRun: 3, // Increased tests per eviction run
  },
  // Add more reliability optimizations
  readonly: false, // Set to true if Redis is read-only
  legacyMode: false, // Disable legacy mode for better performance
  disableOfflineQueue: false // Enable offline queue for better reliability
  // PHASE 4: Connection will be handled manually in connectRedis() function for better control
});

// Set up event handlers
redisClient.on('connect', () => {
  logger.info('Redis client connecting...');
});

redisClient.on('ready', () => {
  logger.info('Redis client connected and ready');
  redisConnected = true;
  consecutiveErrors = 0;
});

redisClient.on('error', (err: Error) => {
  logger.error('Redis client error:', err);
  consecutiveErrors++;

  if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
    logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
    redisConnected = false;
  }
});

redisClient.on('reconnecting', () => {
  logger.info('Redis client reconnecting...');
});

redisClient.on('end', () => {
  logger.info('Redis client disconnected');
  redisConnected = false;
});

// Enhanced local cache with LRU eviction for better performance
export const localCache: {
  [key: string]: {
    value: any;
    expiry: number;
    lastAccessed: number;
    hits: number;
  };
} = {};

// Track cache size for LRU eviction
let localCacheSize = 0;
const MAX_LOCAL_CACHE_SIZE = parseInt(process.env.MAX_LOCAL_CACHE_SIZE || '5000', 10);

// Function to evict least recently used items when cache gets too large
const evictLRUItems = () => {
  if (localCacheSize <= MAX_LOCAL_CACHE_SIZE) return;

  // Sort keys by last accessed time (oldest first)
  const sortedKeys = Object.keys(localCache)
    .sort((a, b) => {
      // Primary sort by hits (less hits first)
      const hitsDiff = (localCache[a].hits || 0) - (localCache[b].hits || 0);
      if (hitsDiff !== 0) return hitsDiff;

      // Secondary sort by last accessed time (oldest first)
      return (localCache[a].lastAccessed || 0) - (localCache[b].lastAccessed || 0);
    });

  // Calculate how many items to remove
  const itemsToRemove = Math.ceil(localCacheSize * 0.2); // Remove 20% of items

  // Remove oldest items
  sortedKeys.slice(0, itemsToRemove).forEach(key => {
    delete localCache[key];
    localCacheSize--;
  });

  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Evicted ${itemsToRemove} least recently used items from local cache`);
  }
};

/**
 * Set a value in the local cache with LRU tracking
 * @param key The cache key
 * @param value The value to cache
 * @param ttl Time to live in seconds
 */
const setInLocalCache = <T>(key: string, value: T, ttl: number): void => {
  const now = Date.now();

  // Check if this is a new key
  if (!(key in localCache)) {
    localCacheSize++;

    // Evict LRU items if cache is too large
    if (localCacheSize > MAX_LOCAL_CACHE_SIZE) {
      evictLRUItems();
    }
  }

  // Store with access tracking
  localCache[key] = {
    value,
    expiry: now + ttl * 1000,
    lastAccessed: now,
    hits: 0
  };
};

/**
 * Get a value from the local cache with LRU tracking
 * @param key The cache key
 * @returns The cached value or null if not found or expired
 */
const getFromLocalCache = <T>(key: string): T | null => {
  const item = localCache[key];
  if (!item) {
    return null;
  }

  const now = Date.now();

  // Check if item is expired
  if (item.expiry < now) {
    delete localCache[key];
    localCacheSize--;
    return null;
  }

  // Update access tracking for LRU algorithm
  item.lastAccessed = now;
  item.hits = (item.hits || 0) + 1;

  return item.value as T;
};

/**
 * Connect to Redis with circuit breaker and timeout protection
 * @returns True if connected successfully, false otherwise
 */
export const connectRedis = async (): Promise<boolean> => {
  try {
    if (!process.env.REDIS_HOST || !process.env.REDIS_PORT || !process.env.REDIS_PASSWORD) {
      logger.warn('Redis host, port, or password not set. Using local memory cache instead.');
      return false;
    }

    logger.info(`Connecting to Redis at ${process.env.REDIS_HOST}:${process.env.REDIS_PORT}...`);

    // Use circuit breaker to protect against Redis connection failures
    return await redisCircuitBreaker.execute(
      async () => {
        // Try to connect to Redis with timeout protection
        let connected = false;
        let retryCount = 0;
        const maxRetries = 3;

        while (!connected && retryCount < maxRetries) {
          try {
            // Add a connection timeout to prevent hanging
            const connectionPromise = redisClient.connect();
            const timeoutPromise = new Promise<void>((_, reject) => {
              setTimeout(() => reject(new Error('Redis connection timeout after 3000ms')), 3000);
            });

            // Race the connection against the timeout
            await Promise.race([connectionPromise, timeoutPromise]);
            connected = true;
            redisConnected = true;
            logger.info('Successfully connected to Redis');
          } catch (connectError: any) {
            retryCount++;
            logger.warn(`Failed to connect to Redis (attempt ${retryCount}/${maxRetries}): ${connectError.message}`);

            if (retryCount < maxRetries) {
              // Wait before retrying (exponential backoff)
              const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
              logger.info(`Retrying Redis connection in ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        }

        if (!connected) {
          logger.error(`Failed to connect to Redis after ${maxRetries} attempts. Using local memory cache only.`);
          return false;
        }

        // Start automatic cache cleanup
        startAutomaticCacheCleanup();

        return true;
      },
      async () => {
        // Fallback function when circuit is open
        logger.warn('Redis circuit breaker is open. Using local memory cache only.');
        redisConnected = false;
        return false;
      }
    );
  } catch (error: any) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`Failed to connect to Redis:`, err);
    logger.info('Falling back to local memory cache only');
    redisConnected = false;
    return false;
  }
};

/**
 * Check if Redis is connected
 * @returns True if Redis is connected, false otherwise
 */
export const isRedisConnected = (): boolean => {
  return redisConnected;
};

/**
 * Gracefully disconnect from Redis
 * @returns Promise that resolves when disconnected
 */
export const disconnectRedis = async (): Promise<void> => {
  try {
    if (redisClient && redisConnected) {
      logger.info('Disconnecting from Redis...');
      await redisClient.quit();
      redisConnected = false;
      logger.info('Redis disconnected successfully');
    }
  } catch (error) {
    logger.error('Error disconnecting from Redis:', error instanceof Error ? error : new Error(String(error)));
    // Force disconnect if graceful quit fails
    try {
      await redisClient.disconnect();
      redisConnected = false;
      logger.info('Redis force disconnected');
    } catch (forceError) {
      logger.error('Error force disconnecting from Redis:', forceError instanceof Error ? forceError : new Error(String(forceError)));
    }
  }
};

// Import the new memory cache
import { getMemoryCache, setMemoryCache } from '../utils/memoryCache';

/**
 * Get a value from the cache with simplified tiered caching strategy
 * 1. Check memory cache first (fastest)
 * 2. Then check Redis (distributed)
 *
 * @param key The cache key
 * @returns The cached value or null if not found
 */
export const getCache = async <T>(key: string): Promise<T | null> => {
  try {
    // TIER 1: Check memory cache first (fastest, no async operations)
    const memValue = getMemoryCache<T>(key);
    if (memValue !== undefined) {
      return memValue;
    }

    // TIER 2: Check local cache (fast, no async operations)
    const localValue = getFromLocalCache<T>(key);
    if (localValue !== null) {
      // Also store in memory cache for even faster access next time
      setMemoryCache(key, localValue, 60);
      return localValue;
    }

    // Fast fail: If Redis is not connected, return null immediately
    if (!isRedisConnected()) {
      return null;
    }

    // TIER 3: Try to get from Redis with circuit breaker and timeout protection
    try {
      // Check if we're at the Redis operation limit
      if (currentRedisOps >= MAX_CONCURRENT_REDIS_OPS) {
        // Skip Redis and return null if too many concurrent operations
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Skipping Redis get for key: ${key} due to connection limit`);
        }
        return null;
      }

      // Increment the operation counter
      currentRedisOps++;

      // Use circuit breaker to protect against Redis failures
      return await redisCircuitBreaker.execute(
        async () => {
          // Add timeout protection
          const getPromise = redisClient.get(key);
          const timeoutPromise = new Promise<string | null>((_, reject) => {
            setTimeout(() => reject(new Error(`Redis get timeout for key: ${key}`)), 3000);
          });

          // Race the get operation against the timeout
          const value = await Promise.race([getPromise, timeoutPromise]);

          if (!value) {
            return null; // Cache miss
          }

          // Optimized JSON parsing with error handling
          let parsedValue: T;
          try {
            // Only parse if the value starts with { or [ (likely JSON)
            if (value.startsWith('{') || value.startsWith('[')) {
              parsedValue = JSON.parse(value) as T;
            } else {
              parsedValue = value as unknown as T;
            }
          } catch (parseError) {
            // If parsing fails, use the raw value
            parsedValue = value as unknown as T;
          }

          // Store in memory cache for faster access next time (60 second TTL)
          setMemoryCache(key, parsedValue, 60);

          // Reset consecutive errors on successful operation
          if (consecutiveErrors > 0) {
            consecutiveErrors = 0;
          }

          return parsedValue;
        },
        async () => {
          // Fallback when circuit is open - return null
          if (process.env.NODE_ENV !== 'production') {
            logger.debug(`Redis circuit breaker open for key: ${key}, using memory cache only`);
          }
          return null;
        }
      );
    } catch (error: any) {
      // Only log errors in production mode, debug in development
      if (process.env.NODE_ENV === 'production') {
        // In production, only log after multiple errors
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          logger.error(`Redis error threshold reached (${consecutiveErrors}). Disabling Redis.`);
          redisConnected = false;
        }
      } else {
        logger.debug(`Redis error for key ${key}: ${error.message}`);
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          logger.debug(`Redis error threshold reached (${consecutiveErrors}). Disabling Redis.`);
          redisConnected = false;
        }
      }

      return null;
    } finally {
      // Decrement the operation counter if we incremented it
      if (currentRedisOps > 0) {
        currentRedisOps--;
      }
    }
  } catch (error) {
    // Only log in development mode to reduce overhead
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Error in getCache: ${error instanceof Error ? error.message : String(error)}`);
    }
    return null;
  }
};

/**
 * Set a value in the cache with simplified tiered caching strategy
 * 1. Set in memory cache (fastest)
 * 2. Set in Redis (distributed)
 *
 * @param key The cache key
 * @param value The value to cache
 * @param ttl Time to live in seconds
 * @returns True if the value was set successfully
 */
export const setCache = async <T>(key: string, value: T, ttl?: number): Promise<boolean> => {
  const ttlToUse = ttl || DEFAULT_TTL;

  // TIER 1: Set in memory cache with shorter TTL (60 seconds)
  setMemoryCache(key, value, 60);

  // PHASE 4: Also set in local cache for fallback
  setInLocalCache(key, value, Math.min(ttlToUse, 300)); // Max 5 minutes for local cache

  // Only log in development mode to reduce overhead
  if (process.env.NODE_ENV !== 'production') {
    logger.debug(`Set in local cache for key: ${key} with TTL: ${ttlToUse}s`);
  }

  // If Redis is not connected, return true (we've set it in local caches)
  if (!isRedisConnected()) {
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Redis not connected, skipping cache set for key: ${key}`);
    }
    return true;
  }

  // TIER 3: Try to set in Redis asynchronously with connection limiting
  try {
    // Check if we're at the Redis operation limit
    if (currentRedisOps >= MAX_CONCURRENT_REDIS_OPS) {
      // Skip Redis if too many concurrent operations
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Skipping Redis set for key: ${key} due to connection limit`);
      }
      return true;
    }

    // Increment the operation counter
    currentRedisOps++;

    try {
      const valueToStore = typeof value === 'string' ? value : JSON.stringify(value);
      await redisClient.set(key, valueToStore, { EX: ttlToUse });

      // Only log in development mode to reduce overhead
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Set in Redis for key: ${key} with TTL: ${ttlToUse}s`);
      }

      // Reset consecutive errors on successful operation
      consecutiveErrors = 0;
      return true;
    } finally {
      // Decrement the operation counter
      if (currentRedisOps > 0) {
        currentRedisOps--;
      }
    }
  } catch (error: any) {
    // Log error but continue since we've set it in local caches
    // In production, only log after multiple errors to reduce noise
    if (process.env.NODE_ENV === 'production') {
      consecutiveErrors++;
      if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
        logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
        redisConnected = false;
      }
    } else {
      logger.error(`Error setting in Redis for key ${key}: ${error.message}`);
      consecutiveErrors++;
      if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
        logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
        redisConnected = false;
      }
    }

    return true;
  }
};

/**
 * Delete a value from the cache with tiered caching strategy
 * 1. Delete from memory cache (fastest)
 * 2. Delete from local cache (fast)
 * 3. Delete from Redis (slower but distributed)
 *
 * @param key The cache key or pattern (if key contains '*')
 * @returns True if the value was deleted successfully
 */
export const deleteCache = async (key: string): Promise<boolean> => {
  const isPattern = key.includes('*');

  if (isPattern) {
    // For patterns, we need to handle differently
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Deleting cache entries matching pattern: ${key}`);
    }

    // Create a RegExp for matching keys
    const keyRegex = new RegExp('^' + key.replace(/\*/g, '.*') + '$');

    // TIER 1: Delete matching keys from memory cache
    try {
      // Import deleteMemoryCache function
      const { deleteMemoryCache } = await import('../utils/memoryCache');

      // For pattern matching, we'll let the memory cache handle it internally
      // Pattern deletion is handled by the memory cache implementation
      deleteMemoryCache(key); // This will handle pattern matching internally
    } catch (error) {
      // Ignore errors with memory cache
    }

    // TIER 2: Delete matching keys from local cache
    const localKeysToDelete = Object.keys(localCache).filter(k => keyRegex.test(k));
    localKeysToDelete.forEach(k => {
      delete localCache[k];
    });

    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Deleted ${localKeysToDelete.length} entries from local cache matching pattern: ${key}`);
    }

    // If Redis is not connected, return true (we've deleted from local caches)
    if (!isRedisConnected()) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Redis not connected, skipping Redis pattern delete for: ${key}`);
      }
      return true;
    }

    // TIER 3: Try to delete matching keys from Redis
    try {
      // Get all keys matching the pattern
      const redisKeys = await redisClient.keys(key);

      if (redisKeys.length > 0) {
        // Delete all matching keys
        await redisClient.del(redisKeys);
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Deleted ${redisKeys.length} entries from Redis matching pattern: ${key}`);
        }
      } else if (process.env.NODE_ENV !== 'production') {
        logger.debug(`No Redis keys found matching pattern: ${key}`);
      }

      // Reset consecutive errors on successful operation
      consecutiveErrors = 0;
      return true;
    } catch (error: any) {
      // Log error but continue since we've deleted from local caches
      if (process.env.NODE_ENV === 'production') {
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
          redisConnected = false;
        }
      } else {
        logger.error(`Error deleting from Redis for pattern ${key}: ${error.message}`);
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
          redisConnected = false;
        }
      }

      return true;
    }
  } else {
    // For exact keys, use the optimized logic

    // TIER 1: Delete from memory cache
    try {
      // Import deleteMemoryCache function
      const { deleteMemoryCache } = await import('../utils/memoryCache');
      deleteMemoryCache(key);
    } catch (error) {
      // Ignore errors with memory cache
    }

    // TIER 2: Delete from local cache
    delete localCache[key];
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Deleted from local cache for key: ${key}`);
    }

    // If Redis is not connected, return true (we've deleted it from local caches)
    if (!isRedisConnected()) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Redis not connected, skipping cache delete for key: ${key}`);
      }
      return true;
    }

    // TIER 3: Try to delete from Redis
    try {
      await redisClient.del(key);
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Deleted from Redis for key: ${key}`);
      }

      // Reset consecutive errors on successful operation
      consecutiveErrors = 0;
      return true;
    } catch (error: any) {
      // Log error but continue since we've deleted it from local caches
      if (process.env.NODE_ENV === 'production') {
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
          redisConnected = false;
        }
      } else {
        logger.error(`Error deleting from Redis for key ${key}: ${error.message}`);
        consecutiveErrors++;
        if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
          logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
          redisConnected = false;
        }
      }

      return true;
    }
  }
};

/**
 * Get all keys matching a pattern
 * @param pattern The pattern to match
 * @returns Array of matching keys
 */
export const getKeys = async (pattern: string): Promise<string[]> => {
  // If Redis is not connected, return empty array
  if (!isRedisConnected()) {
    logger.debug(`Redis not connected, skipping getKeys for pattern: ${pattern}`);
    return [];
  }

  try {
    const keys = await redisClient.keys(pattern);
    // Reset consecutive errors on successful operation
    consecutiveErrors = 0;
    return keys || [];
  } catch (error: any) {
    logger.error(`Error getting keys with pattern ${pattern}: ${error.message}`);
    consecutiveErrors++;

    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
      logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
      redisConnected = false;
    }

    return [];
  }
};

/**
 * PHASE 4: Bulk cache operations using Redis pipeline for better performance
 * @param operations Array of cache operations to execute in batch
 * @returns Array of results for each operation
 */
export const bulkCacheOperations = async (operations: Array<{
  type: 'get' | 'set' | 'del';
  key: string;
  value?: any;
  ttl?: number;
}>): Promise<any[]> => {
  try {
    if (!isRedisConnected() || operations.length === 0) {
      return [];
    }

    // PHASE 4: Use Redis pipeline for bulk operations
    const pipeline = redisClient.multi();

    for (const op of operations) {
      switch (op.type) {
        case 'get':
          pipeline.get(op.key);
          break;
        case 'set':
          if (op.ttl) {
            pipeline.setEx(op.key, op.ttl, JSON.stringify(op.value));
          } else {
            pipeline.set(op.key, JSON.stringify(op.value));
          }
          break;
        case 'del':
          pipeline.del(op.key);
          break;
      }
    }

    const results = await pipeline.exec();

    // Reset consecutive errors on successful operation
    consecutiveErrors = 0;

    return results || [];
  } catch (error) {
    logger.error('Failed to execute bulk cache operations:', error instanceof Error ? error : new Error(String(error)));
    consecutiveErrors++;

    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
      logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
      redisConnected = false;
    }

    return [];
  }
};

/**
 * Get Redis info
 * @returns Redis info or null if not connected
 */
export const getRedisInfo = async (): Promise<any | null> => {
  // If Redis is not connected, return null
  if (!isRedisConnected()) {
    logger.debug('Redis not connected, skipping getRedisInfo');
    return null;
  }

  try {
    const info = await redisClient.info();
    // Reset consecutive errors on successful operation
    consecutiveErrors = 0;
    return info || null;
  } catch (error: any) {
    logger.error(`Error getting Redis info: ${error.message}`);
    consecutiveErrors++;

    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
      logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
      redisConnected = false;
    }

    return null;
  }
};

/**
 * Start automatic cache cleanup to prevent memory leaks
 * PHASE 1: Consolidated cleanup intervals for better performance
 */
const startAutomaticCacheCleanup = () => {
  // PHASE 1: Unified cleanup interval for all cache operations
  const interval = parseInt(process.env.REDIS_CLEANUP_INTERVAL || '300000', 10); // Default: 5 minutes

  // Run cleanup immediately on startup
  setTimeout(() => {
    cleanupLocalCache();
  }, 5000); // Wait 5 seconds after startup

  // Schedule regular cleanup
  setInterval(() => {
    cleanupLocalCache();
  }, interval);
};

/**
 * Clean up local cache by removing expired items and applying LRU eviction
 */
const cleanupLocalCache = () => {
  try {
    const now = Date.now();
    let expiredCount = 0;

    // Remove expired items
    Object.keys(localCache).forEach(key => {
      if (localCache[key].expiry < now) {
        delete localCache[key];
        localCacheSize--;
        expiredCount++;
      }
    });

    // Apply LRU eviction if cache is still too large
    if (localCacheSize > MAX_LOCAL_CACHE_SIZE) {
      evictLRUItems();
    }

    // Log results in development mode
    if (process.env.NODE_ENV !== 'production' && expiredCount > 0) {
      logger.debug(`Cleaned up ${expiredCount} expired entries from local cache. Current size: ${localCacheSize}`);
    }
  } catch (error: any) {
    // Only log in development mode to reduce overhead
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Error during automatic local cache cleanup: ${error.message}`);
    }
  }
};

/**
 * Flush the cache
 * @returns True if the cache was flushed successfully
 */
export const flushCache = async (): Promise<boolean> => {
  // Always flush local cache
  Object.keys(localCache).forEach(key => {
    delete localCache[key];
  });
  logger.info('Local cache flushed');

  // If Redis is not connected, return true (we've flushed local cache)
  if (!isRedisConnected()) {
    logger.debug('Redis not connected, skipping Redis flush');
    return true;
  }

  // Try to flush Redis
  try {
    await redisClient.flushAll();
    logger.info('Redis cache flushed successfully');

    // Reset consecutive errors on successful operation
    consecutiveErrors = 0;
    return true;
  } catch (error: any) {
    // Log error but continue since we've flushed local cache
    logger.error(`Error flushing Redis cache: ${error.message}`);
    consecutiveErrors++;

    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
      logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
      redisConnected = false;
    }

    return true;
  }
};

/**
 * Get cache statistics
 * @returns Cache statistics
 */
export const getCacheStats = async (): Promise<any> => {
  // Get local cache stats
  const localStats = {
    size: Object.keys(localCache).length,
    keys: Object.keys(localCache),
  };

  // If Redis is not connected, return local stats only
  if (!isRedisConnected()) {
    logger.debug('Redis not connected, returning local cache stats only');
    return localStats;
  }

  // Try to get Redis stats
  try {
    const info = await redisClient.info();
    const dbSize = await redisClient.dbSize();

    // Reset consecutive errors on successful operation
    consecutiveErrors = 0;

    // Return combined stats
    return {
      local: localStats,
      redis: {
        info,
        dbSize,
        connected: true,
      }
    };
  } catch (error: any) {
    // Log error but continue with local stats
    logger.error(`Error getting Redis stats: ${error.message}`);
    consecutiveErrors++;

    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
      logger.error(`Too many consecutive Redis errors (${consecutiveErrors}). Disabling Redis.`);
      redisConnected = false;
    }

    return localStats;
  }
};

// Export redisClient which wasn't exported above
export { redisClient };
