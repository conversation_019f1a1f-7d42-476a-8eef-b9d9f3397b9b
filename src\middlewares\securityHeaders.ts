import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to add additional security headers beyond what Helmet provides
 */
export const additionalSecurityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Skip for health check endpoints
  if (req.path === '/health' || req.path === '/healthz') {
    return next();
  }

  try {
    // Only set headers if they haven't been sent yet
    if (!res.headersSent) {
      // Prevent browsers from detecting the MIME type of a file
      res.setHeader('X-Content-Type-Options', 'nosniff');

      // Prevent clickjacking
      res.setHeader('X-Frame-Options', 'DENY');

      // Enable the XSS filter built into most recent web browsers
      res.setHeader('X-XSS-Protection', '1; mode=block');

      // Disable caching for API responses
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // Strict Transport Security (only in production)
      if (process.env.NODE_ENV === 'production') {
        res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
      }

      // Content Security Policy
      res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self'; object-src 'none'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; font-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'");

      // Referrer Policy
      res.setHeader('Referrer-Policy', 'no-referrer');

      // Feature Policy
      res.setHeader('Feature-Policy', "camera 'none'; microphone 'none'; geolocation 'none'");

      // Permissions Policy (newer version of Feature Policy)
      res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
    }
  } catch (error) {
    // Log error but don't fail the request
    console.error('Error in security headers middleware:', error);
  }

  next();
};

export default additionalSecurityHeaders;
