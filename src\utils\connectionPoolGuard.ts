import { Request } from 'express';
import logger from './logger';

// Connection tracking per IP
interface IPConnectionState {
  activeConnections: number;
  totalAttempts: number;
  lastAttempt: number;
  blockedUntil: number;
  suspiciousActivity: boolean;
}

// Regional configuration for connection limits
interface RegionalConnectionConfig {
  maxConnectionsPerIP: number;
  maxConcurrentConnections: number;
  blockDurationMs: number;
  suspiciousThreshold: number;
  monitoringInterval: number;
}

const REGIONAL_CONNECTION_CONFIG: Record<string, RegionalConnectionConfig> = {
  IN: { // India - Conservative limits for 3-core server
    maxConnectionsPerIP: 5,
    maxConcurrentConnections: 30,
    blockDurationMs: 300000,      // 5 minutes
    suspiciousThreshold: 20,      // 20 attempts per minute
    monitoringInterval: 60000,    // 1 minute
  },
  US: { // US Primary - Higher limits
    maxConnectionsPerIP: 15,
    maxConcurrentConnections: 100,
    blockDurationMs: 120000,      // 2 minutes
    suspiciousThreshold: 50,
    monitoringInterval: 60000,
  },
  EU: { // EU - Balanced limits
    maxConnectionsPerIP: 10,
    maxConcurrentConnections: 75,
    blockDurationMs: 180000,      // 3 minutes
    suspiciousThreshold: 40,
    monitoringInterval: 60000,
  }
};

// Pool utilization tracking
interface PoolUtilization {
  writePool: {
    total: number;
    idle: number;
    waiting: number;
    utilization: number;
  };
  readPool: {
    total: number;
    idle: number;
    waiting: number;
    utilization: number;
  };
}

// Connection pool guard class
export class ConnectionPoolGuard {
  private ipConnections = new Map<string, IPConnectionState>();
  private config: RegionalConnectionConfig;
  private region: string;
  private lastCleanup: number = 0;
  private totalActiveConnections: number = 0;
  private emergencyMode: boolean = false;
  private lastEmergencyCheck: number = 0;

  constructor(region?: string) {
    this.region = region || process.env.REGION || 'US';
    this.config = REGIONAL_CONNECTION_CONFIG[this.region] || REGIONAL_CONNECTION_CONFIG.US;
    
    // Start periodic cleanup
    setInterval(() => this.cleanup(), this.config.monitoringInterval);
    
    logger.info(`Connection pool guard initialized for region ${this.region} with limits: ${this.config.maxConnectionsPerIP} per IP, ${this.config.maxConcurrentConnections} total`);
  }

  /**
   * Check if a connection attempt should be allowed
   */
  async guardConnection(ip: string, poolName: 'read' | 'write'): Promise<boolean> {
    const now = Date.now();
    
    // Emergency mode check - if pools are under severe stress
    if (this.shouldEnterEmergencyMode()) {
      if (!this.emergencyMode) {
        this.emergencyMode = true;
        logger.warn(`[SECURITY] Entering emergency mode - connection pools under stress`);
      }
      
      // In emergency mode, be more restrictive
      if (this.getIPConnectionCount(ip) > Math.floor(this.config.maxConnectionsPerIP / 2)) {
        logger.warn(`[SECURITY] Emergency mode: Blocking additional connections from ${ip}`);
        return false;
      }
    } else if (this.emergencyMode) {
      this.emergencyMode = false;
      logger.info(`[SECURITY] Exiting emergency mode - connection pools stabilized`);
    }

    // Get or create IP state
    let ipState = this.ipConnections.get(ip);
    if (!ipState) {
      ipState = {
        activeConnections: 0,
        totalAttempts: 0,
        lastAttempt: now,
        blockedUntil: 0,
        suspiciousActivity: false
      };
      this.ipConnections.set(ip, ipState);
    }

    // Check if IP is currently blocked
    if (now < ipState.blockedUntil) {
      logger.warn(`[SECURITY] Connection blocked for ${ip} (blocked until ${new Date(ipState.blockedUntil).toISOString()})`);
      return false;
    }

    // Check per-IP connection limit
    if (ipState.activeConnections >= this.config.maxConnectionsPerIP) {
      logger.warn(`[SECURITY] Connection limit exceeded for ${ip} (${ipState.activeConnections}/${this.config.maxConnectionsPerIP})`);
      this.blockIP(ip, 'connection_limit');
      return false;
    }

    // Check total connection limit
    if (this.totalActiveConnections >= this.config.maxConcurrentConnections) {
      logger.warn(`[SECURITY] Total connection limit exceeded (${this.totalActiveConnections}/${this.config.maxConcurrentConnections})`);
      return false;
    }

    // Check for suspicious activity (too many attempts)
    const timeSinceLastAttempt = now - ipState.lastAttempt;
    if (timeSinceLastAttempt < 60000) { // Within last minute
      ipState.totalAttempts++;
      if (ipState.totalAttempts > this.config.suspiciousThreshold) {
        logger.warn(`[SECURITY] Suspicious activity detected from ${ip} (${ipState.totalAttempts} attempts in last minute)`);
        ipState.suspiciousActivity = true;
        this.blockIP(ip, 'suspicious_activity');
        return false;
      }
    } else {
      // Reset attempt counter after a minute
      ipState.totalAttempts = 1;
    }

    // Allow connection - update tracking
    ipState.activeConnections++;
    ipState.lastAttempt = now;
    this.totalActiveConnections++;

    logger.debug(`[SECURITY] Connection allowed for ${ip} to ${poolName} pool (${ipState.activeConnections}/${this.config.maxConnectionsPerIP} per IP, ${this.totalActiveConnections}/${this.config.maxConcurrentConnections} total)`);
    
    return true;
  }

  /**
   * Release a connection when it's closed
   */
  releaseConnection(ip: string, poolName: 'read' | 'write'): void {
    const ipState = this.ipConnections.get(ip);
    if (ipState && ipState.activeConnections > 0) {
      ipState.activeConnections--;
      this.totalActiveConnections = Math.max(0, this.totalActiveConnections - 1);
      
      logger.debug(`[SECURITY] Connection released for ${ip} from ${poolName} pool (${ipState.activeConnections}/${this.config.maxConnectionsPerIP} per IP, ${this.totalActiveConnections}/${this.config.maxConcurrentConnections} total)`);
    }
  }

  /**
   * Block an IP address for a specified duration
   */
  private blockIP(ip: string, reason: string): void {
    const ipState = this.ipConnections.get(ip);
    if (ipState) {
      const blockDuration = this.emergencyMode ? this.config.blockDurationMs * 2 : this.config.blockDurationMs;
      ipState.blockedUntil = Date.now() + blockDuration;
      
      logger.warn(`[SECURITY] IP ${ip} blocked for ${blockDuration/1000}s (reason: ${reason})`);
      
      // Audit log for security monitoring
      logger.info(`[AUDIT] IP_BLOCKED: ${ip}, reason: ${reason}, duration: ${blockDuration}ms, region: ${this.region}`);
    }
  }

  /**
   * Get current connection count for an IP
   */
  private getIPConnectionCount(ip: string): number {
    const ipState = this.ipConnections.get(ip);
    return ipState ? ipState.activeConnections : 0;
  }

  /**
   * Check if we should enter emergency mode based on pool utilization
   */
  private shouldEnterEmergencyMode(): boolean {
    const now = Date.now();
    
    // Only check every 30 seconds to avoid overhead
    if (now - this.lastEmergencyCheck < 30000) {
      return this.emergencyMode;
    }
    
    this.lastEmergencyCheck = now;
    
    // Emergency mode if total connections > 80% of limit
    const utilizationThreshold = this.config.maxConcurrentConnections * 0.8;
    return this.totalActiveConnections > utilizationThreshold;
  }

  /**
   * Periodic cleanup of expired IP states
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [ip, state] of this.ipConnections.entries()) {
      // Remove IPs that haven't been active for 10 minutes and aren't blocked
      const inactiveTime = now - state.lastAttempt;
      if (inactiveTime > 600000 && now > state.blockedUntil && state.activeConnections === 0) {
        this.ipConnections.delete(ip);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug(`[SECURITY] Cleaned up ${cleanedCount} inactive IP states`);
    }
    
    // Log current status
    logger.debug(`[SECURITY] Pool guard status: ${this.ipConnections.size} tracked IPs, ${this.totalActiveConnections}/${this.config.maxConcurrentConnections} total connections, emergency mode: ${this.emergencyMode}`);
  }

  /**
   * Get current pool utilization statistics
   */
  getUtilizationStats(): {
    trackedIPs: number;
    totalActiveConnections: number;
    maxConcurrentConnections: number;
    utilizationPercentage: number;
    emergencyMode: boolean;
    region: string;
  } {
    return {
      trackedIPs: this.ipConnections.size,
      totalActiveConnections: this.totalActiveConnections,
      maxConcurrentConnections: this.config.maxConcurrentConnections,
      utilizationPercentage: (this.totalActiveConnections / this.config.maxConcurrentConnections) * 100,
      emergencyMode: this.emergencyMode,
      region: this.region
    };
  }

  /**
   * Get detailed IP statistics for monitoring
   */
  getIPStats(): Array<{
    ip: string;
    activeConnections: number;
    totalAttempts: number;
    lastAttempt: Date;
    blocked: boolean;
    blockedUntil?: Date;
    suspiciousActivity: boolean;
  }> {
    const now = Date.now();
    const stats: Array<any> = [];
    
    for (const [ip, state] of this.ipConnections.entries()) {
      stats.push({
        ip,
        activeConnections: state.activeConnections,
        totalAttempts: state.totalAttempts,
        lastAttempt: new Date(state.lastAttempt),
        blocked: now < state.blockedUntil,
        blockedUntil: state.blockedUntil > 0 ? new Date(state.blockedUntil) : undefined,
        suspiciousActivity: state.suspiciousActivity
      });
    }
    
    return stats.sort((a, b) => b.activeConnections - a.activeConnections);
  }

  /**
   * Manually unblock an IP (for admin use)
   */
  unblockIP(ip: string): boolean {
    const ipState = this.ipConnections.get(ip);
    if (ipState && ipState.blockedUntil > Date.now()) {
      ipState.blockedUntil = 0;
      ipState.suspiciousActivity = false;
      logger.info(`[ADMIN] IP ${ip} manually unblocked`);
      return true;
    }
    return false;
  }

  /**
   * Get blocked IPs list
   */
  getBlockedIPs(): Array<{
    ip: string;
    blockedUntil: Date;
    reason: string;
  }> {
    const now = Date.now();
    const blocked: Array<any> = [];
    
    for (const [ip, state] of this.ipConnections.entries()) {
      if (state.blockedUntil > now) {
        blocked.push({
          ip,
          blockedUntil: new Date(state.blockedUntil),
          reason: state.suspiciousActivity ? 'suspicious_activity' : 'connection_limit'
        });
      }
    }
    
    return blocked;
  }
}

// Create singleton instance
export const connectionPoolGuard = new ConnectionPoolGuard();

// Middleware to integrate with Express requests
export const connectionPoolMiddleware = (req: Request, res: any, next: any): void => {
  // Store IP in request context for database operations
  (req as any).clientIP = req.ip || req.connection.remoteAddress || 'unknown';
  next();
};

// Helper function to get client IP from request context
export const getCurrentRequestIP = (): string => {
  // This would be set by the middleware or extracted from async context
  // For now, return a default that can be overridden
  return 'unknown';
};

// Export configuration for testing
export { REGIONAL_CONNECTION_CONFIG };
