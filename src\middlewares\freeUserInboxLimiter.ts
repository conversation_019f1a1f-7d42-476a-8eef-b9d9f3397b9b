import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { InboxModel } from '../models/Inbox';
import logger from '../utils/logger';
import { FREE_USER_INBOX_LIMIT } from '../config/limits';
import { getCache, setCache, isRedisConnected } from '../config/redis-direct';

// Cache TTL for inbox counts (5 minutes)
const INBOX_COUNT_CACHE_TTL = 300;

// Helper function to get the cache key for inbox count
const getInboxCountCacheKey = (rapidApiKey: string) => `inbox:count:${rapidApiKey}`;

// Simple in-memory lock to prevent race conditions during limit checks
const activeLimitChecks = new Set<string>();

/**
 * Optimized middleware to limit the number of active inboxes for free RapidAPI users
 * - Uses caching to reduce database queries
 * - Implements timeout protection
 * - Adds early exit paths for better performance
 * - Reduces logging in production
 */
export const freeUserInboxLimiter = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let lockTimeout: NodeJS.Timeout | null = null;
  let releaseLock: (() => void) | null = null;

  try {
    // FAST PATH: Only apply this limit to inbox creation requests
    if (req.method !== 'POST') {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`🔄 freeUserInboxLimiter: Skipping non-POST request (${req.method} ${req.path})`);
      }
      return next();
    }

    // FAST PATH: Only apply to RapidAPI users
    if (!(req as any).rapidApi) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`🔄 freeUserInboxLimiter: Skipping request without rapidApi object (${req.method} ${req.path})`);
      }
      return next();
    }

    // Enhanced logging for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug('🔍 freeUserInboxLimiter middleware called for POST request');
      logger.debug(`🔍 Request path: ${req.path}, method: ${req.method}`);
    }

    // Check if this is a free plan user
    // CRITICAL FIX: Use the corrected plan type detection
    const rapidApiData = (req as any).rapidApi;
    const planType = rapidApiData.usage?.planType || '';
    const isFreeUser = planType.toUpperCase() === 'FREE';

    // No need for additional header checks since we now use the correct X-RapidAPI-Subscription header
    const finalIsFreeUser = isFreeUser;

    // Enhanced logging for debugging plan type detection
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`🔍 freeUserInboxLimiter: planType="${planType}", isFreeUser=${isFreeUser}, finalIsFreeUser=${finalIsFreeUser}`);
      logger.debug(`🔍 freeUserInboxLimiter: rapidApi.usage=${JSON.stringify(rapidApiData.usage)}`);
      logger.debug(`🔍 freeUserInboxLimiter: rapidApi object=${JSON.stringify(rapidApiData)}`);

      // Log subscription header for debugging (the correct way to detect free users)
      const subscription = req.headers['x-rapidapi-subscription'] || req.headers['X-RapidAPI-Subscription'] || 'missing';
      logger.debug(`🔍 freeUserInboxLimiter: X-RapidAPI-Subscription header: "${subscription}"`);
    }

    // ULTRA FAST PATH: Check if this request was already marked as fast path
    // CRITICAL FIX: Never skip limit checks for FREE users, even if fast path is enabled
    if ((req as any).fastPath && !finalIsFreeUser) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`freeUserInboxLimiter: Fast path enabled for non-free user, skipping limit checks`);
      }
      return next();
    } else if ((req as any).fastPath && finalIsFreeUser) {
      // Log when fast path is incorrectly set for free users
      logger.warn(`🚨 SECURITY WARNING: Fast path was enabled for a FREE user ${rapidApiData.user} - this should not happen!`);
      // Continue with limit checks despite fast path being set
    }

    // FAST PATH: If not a free user, no limit applies
    if (!finalIsFreeUser) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`freeUserInboxLimiter: Not a free user (planType: ${planType}, finalIsFreeUser: ${finalIsFreeUser}), skipping limit check`);
      }
      return next();
    }

    // Get the RapidAPI key
    const rapidApiKey = (req as any).rapidApi.key;

    // FAST PATH: No RapidAPI key, can't check limit
    if (!rapidApiKey) {
      return next();
    }

    // RACE CONDITION PROTECTION: Check if another request is already checking this user's limit
    const lockKey = `limit-check:${rapidApiKey}`;
    if (activeLimitChecks.has(lockKey)) {
      logger.warn(`Concurrent limit check detected for ${rapidApiKey}, blocking request to prevent race condition`);
      return next(
        new AppError(
          'Another inbox creation is in progress. Please wait a moment and try again.',
          429
        )
      );
    }

    // Acquire lock for this user's limit check
    activeLimitChecks.add(lockKey);

    // Ensure we always release the lock
    releaseLock = () => {
      activeLimitChecks.delete(lockKey);
    };

    // Set up automatic lock release after 10 seconds as a safety measure
    lockTimeout = setTimeout(releaseLock, 10000);

    // CRITICAL: Always get fresh count from database for limit enforcement
    // Cache can be stale due to race conditions, so we need real-time data
    let activeInboxCount: number = 0;

    try {
      const startTime = Date.now();

      // PERFORMANCE OPTIMIZATION: Try cache first for ultra-fast response
      let cachedCount: number | null = null;

      // Check cache first for instant response
      if (isRedisConnected()) {
        try {
          cachedCount = await getCache<number>(getInboxCountCacheKey(rapidApiKey));
          if (cachedCount !== null) {
            const cacheTime = Date.now() - startTime;
            if (process.env.NODE_ENV !== 'production') {
              logger.debug(`Cache hit for inbox count in ${cacheTime}ms for ${rapidApiKey}: ${cachedCount}`);
            }
            activeInboxCount = cachedCount;
          }
        } catch (cacheError) {
          // Cache errors are not critical, continue to database
          if (process.env.NODE_ENV !== 'production') {
            logger.debug('Cache read failed, falling back to database');
          }
        }
      }

      // If cache miss, get from database with aggressive timeout
      if (cachedCount === null) {
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Cache miss, getting inbox count from database for ${rapidApiKey}`);
        }

        // Use the faster count method without cleanup for better performance
        const countPromise = InboxModel.countActiveInboxesByRapidApiKey(rapidApiKey);
        const timeoutPromise = new Promise<number>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Database timeout getting inbox count for ${rapidApiKey}`));
          }, 1500); // Reduced to 1.5 seconds for faster response
        });

        // Race the count operation against the timeout
        activeInboxCount = await Promise.race([countPromise, timeoutPromise]);

        const queryTime = Date.now() - startTime;
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Database inbox count query completed in ${queryTime}ms for ${rapidApiKey}: ${activeInboxCount}`);
        }

        // Performance monitoring: Log slow queries
        if (queryTime > 800) {
          logger.warn(`Slow inbox count query detected: ${queryTime}ms for ${rapidApiKey}`);
        }

        // Update cache asynchronously for future requests (fire and forget)
        if (isRedisConnected()) {
          setCache(getInboxCountCacheKey(rapidApiKey), activeInboxCount, INBOX_COUNT_CACHE_TTL)
            .catch(() => {
              // Ignore cache errors to not slow down the response
            });
        }
      }
    } catch (error) {
      // For limit enforcement, we must be conservative
      // If we can't get the count, we should block the request to prevent abuse
      logger.error('Critical error getting inbox count for limit enforcement:',
        error instanceof Error ? error : new Error(String(error)));

      // Release lock before returning error
      if (lockTimeout) clearTimeout(lockTimeout);
      if (releaseLock) releaseLock();

      return next(
        new AppError(
          'Unable to verify inbox limit. Please try again in a moment.',
          503
        )
      );
    }

    // ENHANCED LIMIT CHECK: Use >= for strict enforcement
    if (activeInboxCount >= FREE_USER_INBOX_LIMIT) {
      // User has reached the limit, return an error
      logger.warn(`🚫 INBOX LIMIT ENFORCED: Free user ${(req as any).rapidApi.user} (RapidAPI key: ${rapidApiKey}) has reached inbox limit (${activeInboxCount}/${FREE_USER_INBOX_LIMIT})`);
      logger.warn(`🚫 Request blocked: POST ${req.path} - Free plan limit exceeded`);

      // Log additional context for monitoring and debugging
      logger.info(`Inbox limit enforcement details: user=${(req as any).rapidApi.user}, rapidApiKey=${rapidApiKey}, activeInboxes=${activeInboxCount}, limit=${FREE_USER_INBOX_LIMIT}, timestamp=${new Date().toISOString()}`);

      // Log request details for audit trail
      logger.info(`Blocked request details: IP=${req.ip}, UserAgent=${req.get('User-Agent')}, Method=${req.method}, Path=${req.path}`);

      // Release lock before returning error
      if (lockTimeout) clearTimeout(lockTimeout);
      if (releaseLock) releaseLock();

      // Enhanced error response with more details for debugging
      const errorMessage = `Free plan users are limited to ${FREE_USER_INBOX_LIMIT} active inboxes. You currently have ${activeInboxCount} active inboxes. Please delete some inboxes or upgrade your plan to create more.`;

      // CRITICAL: Return 400 Bad Request instead of 403 Forbidden for better user experience
      // 400 indicates the request cannot be processed due to client constraints
      // 403 would indicate permission issues which is not accurate here
      return next(
        new AppError(errorMessage, 400)
      );
    }

    // User has not reached the limit, continue
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`✅ Free user is under inbox limit (${activeInboxCount}/${FREE_USER_INBOX_LIMIT}) - allowing inbox creation`);
    }

    // ALWAYS log successful limit checks for monitoring
    logger.info(`✅ INBOX LIMIT CHECK PASSED: Free user ${rapidApiData.user} (${rapidApiKey}) has ${activeInboxCount}/${FREE_USER_INBOX_LIMIT} inboxes - allowing creation`);


    // Release lock before proceeding
    if (lockTimeout) clearTimeout(lockTimeout);
    if (releaseLock) releaseLock();

    next();
  } catch (error) {
    // Log error and release lock
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error in freeUserInboxLimiter:', err);

    // Ensure lock is released even on unexpected errors
    try {
      if (lockTimeout) clearTimeout(lockTimeout);
      if (releaseLock) releaseLock();
    } catch (lockError) {
      logger.error('Error releasing lock:', lockError instanceof Error ? lockError : new Error(String(lockError)));
    }

    // For unexpected errors, be conservative and block the request
    next(new AppError('Inbox limit check failed. Please try again.', 503));
  }
};

export default freeUserInboxLimiter;
