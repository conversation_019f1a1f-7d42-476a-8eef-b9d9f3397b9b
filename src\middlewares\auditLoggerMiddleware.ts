import { Request, Response, NextFunction } from 'express';
import auditLogger from '../utils/auditLogger';

// Attack pattern detection for audit logging optimization
const COMMON_SCAN_PATTERNS = [
  '.env', '.git', 'wp-', 'admin', 'config', 'phpinfo', 'credentials',
  'aws', 'laravel', 'api/swagger', 'api/docs', 'actuator', 'prometheus',
  'metrics', 'health', 'status', 'debug', 'console', 'shell', 'cmd',
  'cgi-bin', 'phpmyadmin', 'mysql', 'db', 'database', 'backup',
  'wp-content', 'wp-admin', 'wp-login', 'xmlrpc.php', 'robots.txt',
  'sitemap.xml', 'favicon.ico'
];

// Attack mode state
let isAttackMode = false;
let lastAttackModeCheck = 0;
const ATTACK_MODE_DURATION = 300000; // 5 minutes

/**
 * Enable attack mode to disable audit logging for scanning patterns
 */
export const enableAuditAttackMode = (): void => {
  if (!isAttackMode) {
    isAttackMode = true;
    console.log('[AUDIT] ATTACK MODE ENABLED - disabling audit logging for scanning patterns');
  }
  lastAttackModeCheck = Date.now();
};

/**
 * Check if we should exit attack mode
 */
const checkAttackModeExpiry = (): void => {
  if (isAttackMode && (Date.now() - lastAttackModeCheck > ATTACK_MODE_DURATION)) {
    isAttackMode = false;
    console.log('[AUDIT] ATTACK MODE DISABLED - resuming normal audit logging');
  }
};

/**
 * Check if a request path matches common scanning patterns
 */
const isCommonScanPattern = (path: string): boolean => {
  return COMMON_SCAN_PATTERNS.some(pattern => path.includes(pattern));
};

/**
 * Ultra-optimized audit logger middleware
 * - Extreme sampling (1 in 1000 for normal requests)
 * - Only logs security-sensitive operations and errors
 * - No overhead for high-traffic endpoints
 * - Minimal data capture
 */
export const requestAuditLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Check attack mode expiry
  checkAttackModeExpiry();

  // ULTRA-FAST PATH: Skip all audit logging for high-traffic endpoints
  if (req.path === '/api/domains' ||
      req.path === '/api/inboxes' && req.method === 'GET' ||
      req.path.includes('/emails') ||
      req.path === '/health' ||
      req.path === '/healthz' ||
      req.path.startsWith('/static/')) {
    return next();
  }

  // ATTACK MODE: Skip audit logging for scanning patterns during attacks
  if (isAttackMode && isCommonScanPattern(req.path)) {
    return next();
  }

  // Only audit POST requests to /api/inboxes for security monitoring
  if (req.path === '/api/inboxes' && req.method !== 'POST') {
    return next();
  }

  // Check if this is a security-sensitive operation
  const isSecuritySensitive =
    req.path.includes('/api/keys') ||
    req.method === 'DELETE' ||
    req.path.includes('/auth');

  // EXTREME SAMPLING: In production, only log 1 in 1000 normal requests
  // During attack mode, increase sampling to 1 in 10000 for non-security operations
  const normalSamplingRate = isAttackMode ? 0.0001 : 0.001;
  if (process.env.NODE_ENV === 'production' && !isSecuritySensitive) {
    if (Math.random() > normalSamplingRate) {
      return next();
    }
  }

  // For security-sensitive operations, use less aggressive sampling (1 in 10)
  // During attack mode, reduce to 1 in 20
  const securitySamplingRate = isAttackMode ? 0.05 : 0.1;
  if (process.env.NODE_ENV === 'production' && isSecuritySensitive) {
    if (Math.random() > securitySamplingRate) {
      return next();
    }
  }

  // Process the request immediately
  next();

  // Log after response is sent - using once to prevent memory leaks
  res.once('finish', () => {
    // Only log errors and security-sensitive operations
    if (res.statusCode >= 400 || isSecuritySensitive) {
      // Capture minimal data
      const method = req.method;
      const path = req.path;
      const statusCode = res.statusCode;
      const requestId = (req as any).requestId;

      // Use setTimeout for minimal overhead
      setTimeout(() => {
        try {
          // Log minimal information
          auditLogger.logRequest({
            method,
            path,
            requestId
          } as any, {
            statusCode
          } as any, 0);
        } catch (e) {
          // Ignore logging errors
        }
      }, 0);
    }
  });
};

/**
 * Ultra-optimized middleware to log security events
 * - Uses sampling even for security events (1 in 5)
 * - Minimal data capture
 * - No overhead for high-traffic endpoints
 */
export const securityAuditLogger = (eventType: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // PHASE 1: ULTRA-FAST PATH - Complete skip for high-traffic endpoints
    if (req.path === '/api/domains' ||
        req.path === '/api/inboxes' && req.method === 'GET' ||
        req.path.includes('/emails') ||
        req.path === '/health' ||
        req.path === '/healthz' ||
        req.path.startsWith('/static/')) {
      return next();
    }

    // SAMPLING: Even for security events, use sampling in production (1 in 5)
    if (process.env.NODE_ENV === 'production' && Math.random() > 0.2) {
      return next();
    }

    // Process the request immediately
    next();

    // Log after response is sent - using once to prevent memory leaks
    res.once('finish', () => {
      // Only log if there was an error
      if (res.statusCode >= 400) {
        // Capture minimal data
        const method = req.method;
        const path = req.path;
        const statusCode = res.statusCode;
        const requestId = (req as any).requestId;

        // Use setTimeout for minimal overhead
        setTimeout(() => {
          try {
            // Log minimal information
            auditLogger.logSecurityEvent(
              {
                method,
                path,
                requestId
              } as any,
              eventType,
              {
                statusCode,
                message: `Security event: ${eventType}`
              }
            );
          } catch (e) {
            // Ignore logging errors
          }
        }, 0);
      }
    });
  };
};

/**
 * Ultra-optimized middleware to log authentication attempts
 * - Only logs failed authentication attempts in production
 * - Uses sampling for successful auth (1 in 100)
 * - Minimal data capture
 * - No overhead for high-traffic endpoints
 */
export const authAuditLogger = (req: Request, res: Response, next: NextFunction): void => {
  // PHASE 1: ULTRA-FAST PATH - Complete skip for high-traffic endpoints
  if (req.path === '/api/domains' ||
      req.path === '/api/inboxes' && req.method === 'GET' ||
      req.path.includes('/emails') ||
      req.path === '/health' ||
      req.path === '/healthz' ||
      req.path.startsWith('/static/')) {
    return next();
  }

  // Process the request immediately
  next();

  // Log after response is sent - using once to prevent memory leaks
  res.once('finish', () => {
    // Determine if authentication was successful based on status code
    const success = res.statusCode < 400;

    // In production, only log failed authentication attempts
    // In development, sample successful auth (1 in 10)
    if ((process.env.NODE_ENV === 'production' && !success) ||
        (process.env.NODE_ENV !== 'production' && (!success || Math.random() < 0.1))) {

      // Capture minimal data
      const method = req.method;
      const path = req.path;
      const statusCode = res.statusCode;
      const requestId = (req as any).requestId;

      // Use setTimeout for minimal overhead
      setTimeout(() => {
        try {
          // Log minimal information
          auditLogger.logSecurityEvent(
            {
              method,
              path,
              requestId,
              // Only include IP for failed auth
              ...(!success ? { ip: req.ip } : {})
            } as any,
            'authentication',
            {
              statusCode,
              // Add message for failed authentication
              ...(!success ? { message: 'Authentication failed' } : {})
            },
            success
          );
        } catch (e) {
          // Ignore logging errors
        }
      }, 0);
    }
  });
};

export default {
  requestAuditLogger,
  securityAuditLogger,
  authAuditLogger,
};
