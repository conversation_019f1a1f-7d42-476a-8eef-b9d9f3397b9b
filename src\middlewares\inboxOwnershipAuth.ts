import { Request, Response, NextFunction } from 'express';
import { InboxModel } from '../models/Inbox';
import { AppError } from './errorHandler';
import logger from '../utils/logger';

/**
 * Middleware to check if the inbox belongs to the API key
 * This ensures that users can only access their own inboxes
 */
export const inboxOwnershipAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get inbox ID from params or URL path
    let inboxId = req.params.inboxId || req.params.id;

    // If inboxId is not in params, try to extract it from the URL path
    if (!inboxId && req.originalUrl) {
      const match = req.originalUrl.match(/\/api\/inboxes\/([^/]+)/);
      if (match && match[1]) {
        inboxId = match[1];
        // Add it to params for downstream middleware
        req.params.inboxId = inboxId;
      }
    }

    if (!inboxId) {
      // Log the params and URL for debugging only in development
      if (process.env.NODE_ENV !== 'production') {
        logger.debug('Params:', JSON.stringify(req.params));
        logger.debug('URL:', req.originalUrl);
      }
      return next(new AppError('Inbox ID is required', 400));
    }

    // Log the inbox ID for debugging only in development
    if (process.env.NODE_ENV !== 'production') {
      logger.debug('Inbox ID:', inboxId);
    }

    // Get API key ID from request
    const apiKeyId = req.apiKey?.id;

    // Get RapidAPI key if the request comes from RapidAPI
    // Only use RapidAPI key for ownership verification if the request actually came through RapidAPI
    const rapidApiKey = (req as any).rapidApi ? (req as any).rapidApi.key : undefined;

    // Log authentication info for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Ownership check with apiKeyId: ${apiKeyId || 'none'}, rapidApiKey: ${rapidApiKey || 'none'}, isRapidApiRequest: ${!!(req as any).rapidApi}`);

      // Log all headers in development mode for debugging
      if (process.env.DEBUG_AUTH === 'true') {
        logger.debug('Auth headers in ownership check:', JSON.stringify({
          'x-rapidapi-proxy-secret': req.headers['x-rapidapi-proxy-secret'] ? 'present' : 'missing',
          'X-RapidAPI-Proxy-Secret': req.headers['X-RapidAPI-Proxy-Secret'] ? 'present' : 'missing',
          'x-rapidapi-key': req.headers['x-rapidapi-key'] ? 'present' : 'missing',
          'X-RapidAPI-Key': req.headers['X-RapidAPI-Key'] ? 'present' : 'missing',
          'x-rapidapi-host': req.headers['x-rapidapi-host'],
          'X-RapidAPI-Host': req.headers['X-RapidAPI-Host'],
          'x-api-key': req.headers['x-api-key'] ? 'present' : 'missing'
        }));
      }
    }

    // Either an API key or RapidAPI key is required
    if (!apiKeyId && !rapidApiKey) {
      logger.warn(`Authentication failed for inbox access: ${inboxId}. No API key or RapidAPI key provided.`);
      return next(new AppError('API key or RapidAPI authentication is required', 401));
    }

    // Get the inbox with timeout protection
    let inbox;
    try {
      const getInboxPromise = InboxModel.getById(inboxId);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Inbox ownership check timeout for inbox ${inboxId}`));
        }, 3000); // 3 second timeout for ownership check
      });

      inbox = await Promise.race([getInboxPromise, timeoutPromise]);

      if (!inbox) {
        return next(new AppError('Inbox not found', 404));
      }
    } catch (error) {
      logger.error(`Error during inbox ownership check for ${inboxId}:`, error instanceof Error ? error : new Error(String(error)));
      return next(new AppError('Failed to verify inbox ownership', 500));
    }

    // Check ownership based on API key or RapidAPI key
    let hasOwnership = false;

    // Check API key ownership if available
    if (apiKeyId && inbox.api_key_id) {
      hasOwnership = inbox.api_key_id === apiKeyId;
    }

    // Check RapidAPI key ownership if available
    if (!hasOwnership && rapidApiKey && inbox.rapidapi_key) {
      hasOwnership = inbox.rapidapi_key === rapidApiKey;
    }

    // If the inbox has an owner and the current user is not the owner, deny access
    if ((inbox.api_key_id || inbox.rapidapi_key) && !hasOwnership) {
      return next(new AppError('You do not have permission to access this inbox', 403));
    }

    // Log ownership information for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Inbox ownership check: apiKeyId=${apiKeyId}, rapidApiKey=${rapidApiKey}, hasOwnership=${hasOwnership}`);
    }

    // Continue to next middleware
    next();
  } catch (error) {
    next(error);
  }
};
