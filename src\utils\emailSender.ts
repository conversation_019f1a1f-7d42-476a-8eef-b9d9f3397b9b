import nodemailer from 'nodemailer';
import logger from './logger';

// Create a transporter
let transporter: nodemailer.Transporter;

// Initialize the transporter
const initializeTransporter = () => {
  if (transporter) return;

  // Check if SMTP settings are configured
  if (
    !process.env.SMTP_HOST ||
    !process.env.SMTP_PORT ||
    !process.env.SMTP_USER ||
    !process.env.SMTP_PASS ||
    !process.env.SMTP_FROM
  ) {
    logger.warn('SMTP settings not fully configured. Email sending will not work.');
    return;
  }

  // Create the transporter
  transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT, 10),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

/**
 * Send an email
 */
export const sendEmail = async (
  to: string,
  subject: string,
  text: string,
  html?: string
): Promise<boolean> => {
  try {
    // Initialize the transporter if not already done
    initializeTransporter();

    // If transporter is not available, log and return false
    if (!transporter) {
      logger.error('Email transporter not available. Cannot send email.');
      return false;
    }

    // Send the email
    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM || '',
      to,
      subject,
      text,
      html: html || text,
    });

    logger.info(`Email sent: ${info.messageId}`);
    return true;
  } catch (error: any) {
    logger.error('Error sending email:', new Error(error?.message || 'Unknown error'));
    return false;
  }
};

/**
 * Forward an email
 * Note: Forwarding functionality is disabled for now
 */
/*
export const forwardEmail = async (
  to: string,
  from: string,
  subject: string,
  text: string,
  html: string,
  originalHeaders: any,
  attachmentInfo?: any[]
): Promise<boolean> => {
  try {
    // Initialize the transporter if not already done
    initializeTransporter();

    // If transporter is not available, log and return false
    if (!transporter) {
      logger.error('Email transporter not available. Cannot forward email.');
      return false;
    }

    // Prepare the forwarded email
    const forwardedSubject = `Fwd: ${subject}`;

    // Create forwarded text content
    const forwardedText = `
---------- Forwarded message ---------
From: ${from}
Date: ${new Date().toLocaleString()}
Subject: ${subject}
To: ${to}

${text}

${attachmentInfo && attachmentInfo.length > 0
  ? `\n\nAttachments: ${attachmentInfo.map(a => `${a.filename} (${a.size} bytes)`).join(', ')}`
  : ''}
`;

    // Create forwarded HTML content
    const forwardedHtml = `
<div style="border-left: 1px solid #ccc; padding-left: 10px; margin-left: 10px;">
  <p><strong>---------- Forwarded message ---------</strong></p>
  <p><strong>From:</strong> ${from}</p>
  <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
  <p><strong>Subject:</strong> ${subject}</p>
  <p><strong>To:</strong> ${to}</p>
</div>
<div style="margin-top: 20px;">
  ${html}
</div>
${attachmentInfo && attachmentInfo.length > 0
  ? `<div style="margin-top: 20px; border-top: 1px solid #eee; padding-top: 10px;">
      <p><strong>Attachments:</strong> ${attachmentInfo.map(a => `${a.filename} (${a.size} bytes)`).join(', ')}</p>
      <p style="color: #888; font-size: 0.9em;">Note: Attachments are not included in forwarded emails for security reasons.</p>
     </div>`
  : ''}
`;

    // Send the forwarded email
    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM || '',
      to,
      subject: forwardedSubject,
      text: forwardedText,
      html: forwardedHtml,
      headers: {
        'X-Forwarded-By': 'TempMail Service',
        'X-Original-To': originalHeaders.to || 'unknown',
      }
    });

    logger.info(`Forwarded email sent: ${info.messageId}`);
    return true;
  } catch (error: any) {
    logger.error('Error forwarding email:', new Error(error?.message || 'Unknown error'));
    return false;
  }
};
*/
