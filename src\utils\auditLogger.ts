import * as winston from 'winston';
import 'winston-daily-rotate-file';
import * as path from 'path';
import * as fs from 'fs';
import { Request, Response } from 'express';

// Import winston types
import { TransformableInfo } from 'logform';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Create separate transports for different log types
const requestTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logsDir, 'request-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  )
});

const securityTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logsDir, 'security-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '30d', // Keep security logs longer
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  )
});

const errorTransport = new winston.transports.DailyRotateFile({
  filename: path.join(logsDir, 'error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  level: 'error',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  )
});

// Create loggers
const requestLogger = winston.createLogger({
  level: 'info',
  transports: [requestTransport]
});

const securityLogger = winston.createLogger({
  level: 'info',
  transports: [securityTransport]
});

const errorLogger = winston.createLogger({
  level: 'error',
  transports: [errorTransport]
});

// Add console transport in development mode
if (process.env.NODE_ENV !== 'production') {
  const consoleFormat = winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.printf((info: TransformableInfo) => {
      const { timestamp, level, message, ...meta } = info;
      return `[${timestamp}] ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
    })
  );

  requestLogger.add(new winston.transports.Console({ format: consoleFormat }));
  securityLogger.add(new winston.transports.Console({ format: consoleFormat }));
  errorLogger.add(new winston.transports.Console({ format: consoleFormat }));
}

// Mask sensitive data in logs
const maskSensitiveData = (obj: any): any => {
  if (!obj) return obj;

  const maskedObj = { ...obj };

  // Mask API keys
  if (maskedObj.headers && maskedObj.headers['x-api-key']) {
    maskedObj.headers['x-api-key'] = maskedObj.headers['x-api-key'].substring(0, 8) + '********';
  }

  // Mask authorization headers
  if (maskedObj.headers && maskedObj.headers.authorization) {
    maskedObj.headers.authorization = '********';
  }

  // Mask passwords in request bodies
  if (maskedObj.body && maskedObj.body.password) {
    maskedObj.body.password = '********';
  }

  return maskedObj;
};

// Sanitize request object for logging
const sanitizeRequest = (req: Request): any => {
  return {
    id: (req as any).requestId,
    method: req.method,
    url: req.originalUrl,
    params: req.params,
    query: req.query,
    headers: {
      'user-agent': req.headers['user-agent'],
      'content-type': req.headers['content-type'],
      'x-api-key': req.headers['x-api-key'] ? '********' : undefined,
      'x-forwarded-for': req.headers['x-forwarded-for'],
      'x-real-ip': req.headers['x-real-ip'],
    },
    body: req.method !== 'GET' ? maskSensitiveData(req.body) : undefined,
    ip: req.ip,
    apiKeyId: (req as any).apiKey?.id,
  };
};

// Log request
export const logRequest = (req: Request, res: Response, responseTime?: number): void => {
  const sanitizedReq = sanitizeRequest(req);

  requestLogger.info('API Request', {
    request: sanitizedReq,
    response: {
      statusCode: res.statusCode,
      responseTime: responseTime ? `${responseTime}ms` : undefined,
    },
  });
};

// Log security event
export const logSecurityEvent = (
  req: Request,
  eventType: string,
  details: any,
  success: boolean = true
): void => {
  const sanitizedReq = sanitizeRequest(req);

  securityLogger.info('Security Event', {
    request: sanitizedReq,
    event: {
      type: eventType,
      success,
      details: maskSensitiveData(details),
    },
  });
};

// Log error
export const logError = (
  req: Request,
  error: Error,
  statusCode?: number
): void => {
  const sanitizedReq = sanitizeRequest(req);

  errorLogger.error('API Error', {
    request: sanitizedReq,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      statusCode,
    },
  });
};

// Log application error (not related to a specific request)
export const logAppError = (
  message: string,
  error: Error,
  context?: any
): void => {
  errorLogger.error('Application Error', {
    message,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    context: maskSensitiveData(context),
  });
};

export default {
  logRequest,
  logSecurityEvent,
  logError,
  logAppError,
};
