import { Api<PERSON>ey } from '../../models/ApiKey';

declare global {
  namespace Express {
    interface Request {
      apiKey?: ApiKey;
      requestId?: string;
      originalApiKeyId?: string;
      rapidApi?: {
        key: string;
        user: string;
        // Usage information
        usage?: {
          limit: number;
          remaining: number;
          reset: number;
          planType: string;
        };
      };
    }

    interface Response {
      // Add methods for setting RapidAPI headers
      setRapidApiHeaders?: (remaining: number, limit: number, reset: number) => void;
    }
  }
}

// This is needed to make the file a module
export {};
