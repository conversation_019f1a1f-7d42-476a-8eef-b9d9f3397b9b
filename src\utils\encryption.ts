import crypto from 'crypto';
import logger from './logger';

// Constants
const ALGORITHM = 'aes-256-cbc';
const IV_LENGTH = 16; // For AES, this is always 16
const KEY_LENGTH = 32; // 256 bits

// Get encryption key from environment variables or generate a secure one
// Note: In production, ENCRYPTION_KEY should always be set in environment variables
// and should be consistent across restarts
let ENCRYPTION_KEY: string;

if (process.env.ENCRYPTION_KEY) {
  // Use the provided key, but ensure it's the right length
  const keyBuffer = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
  if (keyBuffer.length !== KEY_LENGTH) {
    logger.warn(`ENCRYPTION_KEY has incorrect length. Expected ${KEY_LENGTH} bytes, got ${keyBuffer.length} bytes. Using a derived key.`);
    // Derive a key of the correct length using PBKDF2
    const derivedKey = crypto.pbkdf2Sync(process.env.ENCRYPTION_KEY, 'salt', 10000, KEY_LENGTH, 'sha256');
    ENCRYPTION_KEY = derivedKey.toString('hex');
  } else {
    ENCRYPTION_KEY = process.env.ENCRYPTION_KEY;
  }
} else {
  // Generate a secure random key - but warn that this is not suitable for production
  ENCRYPTION_KEY = crypto.randomBytes(KEY_LENGTH).toString('hex');
  logger.warn('No ENCRYPTION_KEY provided in environment variables. Generated a random key, which will change on restart. This is NOT suitable for production use.');
}

/**
 * Encrypt data using AES-256-CBC
 * @param data - Data to encrypt (string or object)
 * @returns Encrypted data as a string
 */
export const encrypt = (data: any): string => {
  try {
    // Convert data to string if it's an object
    const text = typeof data === 'object' ? JSON.stringify(data) : String(data);

    // Create an initialization vector
    const iv = crypto.randomBytes(IV_LENGTH);

    // Validate IV length
    if (iv.length !== IV_LENGTH) {
      throw new Error(`IV length must be ${IV_LENGTH} bytes`);
    }

    // Create cipher using the encryption key and iv
    const cipher = crypto.createCipheriv(
      ALGORITHM,
      Buffer.from(ENCRYPTION_KEY, 'hex'),
      iv
    );

    // Encrypt the data
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Combine iv and encrypted data
    return `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    logger.error('Encryption failed', error as Error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypt data using AES-256-CBC
 * @param encryptedData - Encrypted data string
 * @returns Decrypted data
 */
export const decrypt = (encryptedData: string): any => {
  try {
    // Split iv and encrypted data
    const parts = encryptedData.split(':');
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];

    // Validate IV length
    if (iv.length !== IV_LENGTH) {
      throw new Error(`Invalid IV length: ${iv.length}. Expected: ${IV_LENGTH}`);
    }

    // Create decipher using the encryption key and iv
    const decipher = crypto.createDecipheriv(
      ALGORITHM,
      Buffer.from(ENCRYPTION_KEY, 'hex'),
      iv
    );

    // Decrypt the data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    // Try to parse as JSON if possible
    try {
      return JSON.parse(decrypted);
    } catch {
      // Return as string if not valid JSON
      return decrypted;
    }
  } catch (error) {
    logger.error('Decryption failed', error as Error);
    throw new Error('Failed to decrypt data');
  }
};

/**
 * Encrypt sensitive fields in an object
 * @param data - Object containing data to encrypt
 * @param sensitiveFields - Array of field names to encrypt
 * @returns Object with encrypted sensitive fields
 */
export const encryptSensitiveFields = (
  data: Record<string, any>,
  sensitiveFields: string[]
): Record<string, any> => {
  try {
    const result = { ...data };

    for (const field of sensitiveFields) {
      if (result[field] !== undefined) {
        result[field] = encrypt(result[field]);
      }
    }

    return result;
  } catch (error) {
    logger.error('Failed to encrypt sensitive fields', error as Error);
    return data; // Return original data on error
  }
};

/**
 * Decrypt sensitive fields in an object
 * @param data - Object containing encrypted data
 * @param sensitiveFields - Array of field names to decrypt
 * @returns Object with decrypted sensitive fields
 */
export const decryptSensitiveFields = (
  data: Record<string, any>,
  sensitiveFields: string[]
): Record<string, any> => {
  try {
    const result = { ...data };

    for (const field of sensitiveFields) {
      if (result[field] !== undefined && typeof result[field] === 'string') {
        try {
          result[field] = decrypt(result[field]);
        } catch (error) {
          // Skip fields that can't be decrypted
          logger.warn(`Failed to decrypt field: ${field} - ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    }

    return result;
  } catch (error) {
    logger.error('Failed to decrypt sensitive fields', error as Error);
    return data; // Return original data on error
  }
};

export default {
  encrypt,
  decrypt,
  encryptSensitiveFields,
  decryptSensitiveFields,
};
