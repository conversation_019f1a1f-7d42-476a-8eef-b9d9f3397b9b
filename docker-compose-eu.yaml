services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"  # EU server likely has better specs than India/HK
          memory: 3g   # More memory for EU server
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 25s  # Slightly faster than India due to better connectivity
      timeout: 12s   # Shorter timeout for EU
      retries: 4     # Moderate retries for EU
      start_period: 35s  # Shorter start period for EU
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=eu-1
      - REGION=eu  # CRITICAL FIX: Proper region detection

      # CRITICAL FIX: Multi-region read/write splitting configuration
      # Write operations MUST go to US primary database (*************)
      # Read operations can use local EU replica (host.docker.internal)
      - DB_WRITE_HOST=*************
      - DB_WRITE_PORT=5432
      - DB_WRITE_DATABASE=tempfly_app
      - DB_WRITE_USER=postgres
      - DB_WRITE_PASSWORD=4wyWCAAk92hkGUhdh7
      - DB_WRITE_SSLMODE=disable

      # Local EU read replica for faster read operations
      - DB_READ_HOST=host.docker.internal
      - DB_READ_PORT=5432
      - DB_READ_DATABASE=tempfly_app
      - DB_READ_USER=postgres
      - DB_READ_PASSWORD=4wyWCAAk92hkGUhdh7
      - DB_READ_SSLMODE=disable

      # Legacy environment variables for backward compatibility
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # EU-specific database connection settings (moderate timeouts)
      - DB_CONNECTION_TIMEOUT=15000  # 15s for EU (vs 25s India)
      - DB_ACQUIRE_TIMEOUT=7500      # 7.5s for EU (vs 12.5s India)
      - DB_STATEMENT_TIMEOUT=60000   # 60s for EU (vs 112.5s India)
      - DB_QUERY_TIMEOUT=60000       # 60s for EU (vs 112.5s India)
      - DB_POOL_MAX=12               # Higher pool for better EU server
      - DB_POOL_MIN=2                # Minimum connections
      - DB_IDLE_TIMEOUT=75000        # 75s idle timeout for EU
      # Health check settings for EU
      - DB_HEALTH_CHECK_INTERVAL=30000  # 30s health check interval
      - DB_MAX_CONSECUTIVE_FAILURES=4   # Moderate failure tolerance
      - DB_MAX_RETRIES=4                # Moderate retries for EU
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=5000      # Shorter Redis timeout for EU
      - REDIS_COMMAND_TIMEOUT=4000      # Shorter Redis command timeout
      # Application settings optimized for EU server
      - CONNECTION_WARMUP_INTERVAL=45000  # 45s warmup interval for EU
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-200}  # More cache for EU
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-1200}  # More items for EU
      # Logging settings for EU
      - LOG_LEVEL=info
      - ENABLE_REQUEST_LOGGING=true
      - ENABLE_PERFORMANCE_MONITORING=true  # Enable for monitoring
      # Rate limiting for EU server
      - RATE_LIMIT_MAX_REQUESTS=400         # Higher limit for EU server
      - RATE_LIMIT_WINDOW_MS=60000
      # Other environment variables
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-10}  # Fewer log files for EU
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-5}     # Smaller log files for EU
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"  # More CPU for Redis on EU server
          memory: 1g   # More memory for Redis on EU
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s  # Standard interval for EU
      timeout: 8s    # Shorter timeout for EU
      retries: 3     # Standard retries
      start_period: 10s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
