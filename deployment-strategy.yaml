# TempFly.io API Deployment Strategy Configuration
# This file defines the rolling deployment strategy to prevent timeout issues

version: "3.9"

# Rolling deployment configuration
x-deployment-config: &deployment-config
  deploy:
    replicas: 1
    update_config:
      parallelism: 1  # Deploy one instance at a time
      delay: 10s      # Wait 30 seconds between deployments
      failure_action: rollback
      monitor: 30s    # Monitor for 60 seconds before considering successful
      max_failure_ratio: 0.1
      order: start-first  # Start new container before stopping old one
    restart_policy:
      condition: on-failure
      delay: 5s
      max_attempts: 3
      window: 120s
    rollback_config:
      parallelism: 1
      delay: 10s
      failure_action: pause
      monitor: 30s
      max_failure_ratio: 0.1
      order: stop-first

# Enhanced health check configuration
x-healthcheck-config: &healthcheck-config
  healthcheck:
    test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/internal/readiness"]
    interval: 10s
    timeout: 3s
    retries: 3
    start_period: 15s


# Resource limits optimized for deployment stability
x-resource-limits: &resource-limits
  deploy:
    resources:
      limits:
        cpus: "1.0"
        memory: 4g
      reservations:
        cpus: "0.5"
        memory: 2g

# Environment variables for deployment optimization
x-deployment-env: &deployment-env
  # Deployment-specific settings
  - DEPLOYMENT_MODE=rolling
  - GRACEFUL_SHUTDOWN_TIMEOUT=30000
  - STARTUP_PROBE_DELAY=5000
  - READINESS_PROBE_DELAY=10000
  # Connection pool settings optimized for deployments
  - PG_MAX_CONNECTIONS=15  # Reduced during deployments
  - PG_MIN_CONNECTIONS=3
  - PG_IDLE_TIMEOUT=20000
  # Redis settings for deployment stability
  - REDIS_CONNECT_TIMEOUT=5000
  - REDIS_COMMAND_TIMEOUT=3000
  - REDIS_RETRY_ATTEMPTS=3
  # Cache warming settings
  - CACHE_WARMUP_ENABLED=true
  - CACHE_WARMUP_DELAY=15000  # Wait 15s before warming cache
  - CACHE_WARMUP_TIMEOUT=30000

# Pre-deployment hooks
x-pre-deployment: &pre-deployment
  # Commands to run before deployment
  - name: "Database Connection Test"
    command: "pg_isready -h ${PGHOST} -p ${PGPORT} -U ${PGUSER} -d ${PGDATABASE}"
    timeout: 10s
  - name: "Redis Connection Test"
    command: "redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} -a ${REDIS_PASSWORD} ping"
    timeout: 5s
  - name: "Cache Preload"
    command: "curl -f http://localhost:3000/api/internal/warmup"
    timeout: 30s

# Post-deployment hooks
x-post-deployment: &post-deployment
  # Commands to run after deployment
  - name: "Health Check Verification"
    command: "curl -f http://localhost:3000/api/internal/readiness"
    timeout: 10s
    retries: 5
    delay: 5s
  - name: "Performance Test"
    command: "curl -f -w '%{time_total}' http://localhost:3000/api/domains"
    timeout: 15s
    max_response_time: 2.0  # Fail if response time > 2 seconds

# Rollback configuration
x-rollback-config: &rollback-config
  rollback:
    triggers:
      - health_check_failure
      - performance_degradation
      - error_rate_threshold
    conditions:
      max_error_rate: 0.1  # 10% error rate
      max_response_time: 5.0  # 5 seconds
      min_success_rate: 0.9  # 90% success rate
    timeout: 120s  # Rollback timeout

# Monitoring configuration during deployment
x-monitoring-config: &monitoring-config
  monitoring:
    metrics:
      - response_time
      - error_rate
      - memory_usage
      - cpu_usage
      - database_connections
      - redis_connections
    alerts:
      - name: "High Error Rate"
        condition: "error_rate > 0.1"
        action: "rollback"
      - name: "High Response Time"
        condition: "avg_response_time > 3.0"
        action: "alert"
      - name: "Memory Usage"
        condition: "memory_usage > 0.9"
        action: "alert"
    collection_interval: 10s
    retention_period: 24h
