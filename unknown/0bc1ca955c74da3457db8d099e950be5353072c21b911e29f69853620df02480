import { Request, Response, NextFunction } from 'express';
import encryption from '../utils/encryption';
import logger from '../utils/logger';
import auditLogger from '../utils/auditLogger';

// Define sensitive fields that should be encrypted/decrypted
const SENSITIVE_FIELDS = [
  'password',
  'apiKey',
  'token',
  'secret',
  'forwarding_email',
  'email',
  'phone',
  'address',
];

/**
 * Middleware to encrypt sensitive fields in response
 */
export const encryptResponsePayload = (req: Request, res: Response, next: NextFunction): void => {
  // Skip encryption for health check endpoints
  if (req.path === '/health' || req.path === '/healthz') {
    return next();
  }

  // Store the original json method
  const originalJson = res.json;

  // Override the json method
  res.json = function(body: any): Response {
    try {
      // Only encrypt if encryption is enabled and the response is successful
      if (process.env.ENABLE_PAYLOAD_ENCRYPTION === 'true' && res.statusCode < 400) {
        // Check if the client supports encryption
        const clientSupportsEncryption = req.headers['x-accept-encryption'] === 'true';

        if (clientSupportsEncryption) {
          // Add encryption header
          res.setHeader('X-Content-Encrypted', 'true');

          // Encrypt sensitive fields in the response
          if (body && typeof body === 'object') {
            // Handle nested data object
            if (body.data && typeof body.data === 'object') {
              body.data = encryption.encryptSensitiveFields(body.data, SENSITIVE_FIELDS);
            }

            // Handle arrays of objects
            if (body.data && Array.isArray(body.data)) {
              body.data = body.data.map((item: any) => {
                if (item && typeof item === 'object') {
                  return encryption.encryptSensitiveFields(item, SENSITIVE_FIELDS);
                }
                return item;
              });
            }
          }

          // Log encryption event
          auditLogger.logSecurityEvent(req, 'payload-encryption', {
            direction: 'response',
            encrypted: true,
          });
        }
      }

      // Call the original json method
      return originalJson.call(this, body);
    } catch (error) {
      logger.error('Response encryption failed', error as Error);
      return originalJson.call(this, body);
    }
  };

  next();
};

/**
 * Middleware to decrypt sensitive fields in request
 */
export const decryptRequestPayload = (req: Request, res: Response, next: NextFunction): void => {
  // Skip decryption for health check endpoints
  if (req.path === '/health' || req.path === '/healthz') {
    return next();
  }

  try {
    // Only decrypt if encryption is enabled and the client indicates encryption
    if (
      process.env.ENABLE_PAYLOAD_ENCRYPTION === 'true' &&
      req.headers['x-content-encrypted'] === 'true'
    ) {
      // Decrypt request body if it exists
      if (req.body && typeof req.body === 'object') {
        req.body = encryption.decryptSensitiveFields(req.body, SENSITIVE_FIELDS);

        // Log decryption event
        auditLogger.logSecurityEvent(req, 'payload-encryption', {
          direction: 'request',
          decrypted: true,
        });
      }
    }

    next();
  } catch (error) {
    logger.error('Request decryption failed', error as Error);
    next(error);
  }
};

export default {
  encryptResponsePayload,
  decryptRequestPayload,
};
