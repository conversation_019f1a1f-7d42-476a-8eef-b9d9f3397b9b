/**
 * Utility functions for generating random values
 */

/**
 * Generate a random string of specified length
 * @param length - Length of the random string
 * @param charset - Character set to use (default: alphanumeric)
 * @returns Random string
 */
export const generateRandomString = (
  length: number,
  charset: string = 'abcdefghijklmnopqrstuvwxyz0123456789'
): string => {
  let result = '';
  const charsetLength = charset.length;
  
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charsetLength));
  }
  
  return result;
};

/**
 * Generate a random name for an inbox
 * Format: random string of 8-10 characters
 * @returns Random name suitable for an inbox
 */
export const generateRandomInboxName = (): string => {
  // Generate a random length between 8 and 10
  const length = Math.floor(Math.random() * 3) + 8;
  
  // Generate a random string with letters and numbers
  return generateRandomString(length);
};
