/**
 * Configuration for various limits in the application
 */
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Maximum number of active inboxes allowed for free RapidAPI users
export const FREE_USER_INBOX_LIMIT = parseInt(process.env.FREE_USER_INBOX_LIMIT || '10');

// RapidAPI tier limits (requests per month)
export const RAPIDAPI_TIER_LIMITS = {
  FREE: parseInt(process.env.RAPIDAPI_TIER_FREE || '1000'),      // Default: 1,000 requests per month
  BASIC: parseInt(process.env.RAPIDAPI_TIER_BASIC || '1000'),    // Default: 1,000 requests per month
  PRO: parseInt(process.env.RAPIDAPI_TIER_PRO || '10000'),       // Default: 10,000 requests per month
  BUSINESS: parseInt(process.env.RAPIDAPI_TIER_BUSINESS || '40000'), // Default: 40,000 requests per month
  ENTERPRISE: parseInt(process.env.RAPIDAPI_TIER_ENTERPRISE || '150000') // Default: 150,000 requests per month
};

// Default rate limits
export const DEFAULT_RATE_LIMITS = {
  WINDOW_MS: parseInt(process.env.DEFAULT_RATE_LIMIT_WINDOW_MS || '60000'), // Default: 1 minute
  MAX_REQUESTS: parseInt(process.env.DEFAULT_RATE_LIMIT_MAX_REQUESTS || '100') // Default: 100 requests per minute
};
