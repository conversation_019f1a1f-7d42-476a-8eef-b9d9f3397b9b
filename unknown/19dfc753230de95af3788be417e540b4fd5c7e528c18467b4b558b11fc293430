import Joi from 'joi';

// Schema for creating a new API key
export const createApiKeySchema = Joi.object({
  name: Joi.string()
    .min(3)
    .max(100)
    .required()
    .messages({
      'string.base': 'Name must be a string',
      'string.empty': 'Name is required',
      'string.min': 'Name must be at least 3 characters long',
      'string.max': 'Name cannot be longer than 100 characters',
      'any.required': 'Name is required',
    }),
  
  expiresIn: Joi.number()
    .integer()
    .min(1)
    .messages({
      'number.base': 'Expiration days must be a number',
      'number.integer': 'Expiration days must be an integer',
      'number.min': 'Expiration days must be at least 1',
    }),
});

// Schema for API key ID parameter
export const apiKeyIdSchema = Joi.object({
  id: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.base': 'API key ID must be a string',
      'string.empty': 'API key ID is required',
      'string.uuid': 'API key ID must be a valid UUID',
      'any.required': 'API key ID is required',
    }),
});
