import logger from './logger';

// List of allowed content types for attachments
export const ALLOWED_CONTENT_TYPES = [
  // Document types
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/csv',
  'application/rtf',
  
  // Image types
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/bmp',
   
  // Other common types
  'application/json',
  'application/xml',
  'text/calendar',
  'application/ics'
];

// Map of file extensions to expected content types
export const FILE_EXTENSION_MAP: Record<string, string[]> = {
  '.pdf': ['application/pdf'],
  '.doc': ['application/msword'],
  '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  '.xls': ['application/vnd.ms-excel'],
  '.xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  '.ppt': ['application/vnd.ms-powerpoint'],
  '.pptx': ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
  '.txt': ['text/plain'],
  '.csv': ['text/csv'],
  '.rtf': ['application/rtf'],
  '.jpg': ['image/jpeg'],
  '.jpeg': ['image/jpeg'],
  '.png': ['image/png'],
  '.gif': ['image/gif'],
  '.webp': ['image/webp'],
  '.bmp': ['image/bmp'],
  '.json': ['application/json'],
  '.xml': ['application/xml', 'text/xml'],
  '.ics': ['text/calendar', 'application/ics']
};

/**
 * Validates if a content type is allowed
 * @param contentType The content type to validate
 * @returns True if the content type is allowed, false otherwise
 */
export const isContentTypeAllowed = (contentType: string): boolean => {
  // Normalize content type (lowercase and trim)
  const normalizedContentType = contentType.toLowerCase().trim();
  
  // Check if content type is in the allowed list
  return ALLOWED_CONTENT_TYPES.includes(normalizedContentType);
};

/**
 * Validates if a file extension matches the expected content type
 * @param filename The filename to check
 * @param contentType The content type to validate against
 * @returns True if the extension matches the content type, false otherwise
 */
export const isExtensionMatchingContentType = (filename: string, contentType: string): boolean => {
  // If no filename, can't validate
  if (!filename) return false;
  
  // Get file extension
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) return false; // No extension
  
  const extension = filename.slice(lastDotIndex).toLowerCase();
  const normalizedContentType = contentType.toLowerCase().trim();
  
  // Check if extension is in our map
  const expectedContentTypes = FILE_EXTENSION_MAP[extension];
  if (!expectedContentTypes) return false; // Unknown extension
  
  // Check if content type matches expected types for this extension
  return expectedContentTypes.includes(normalizedContentType);
};

/**
 * Validates an attachment for security
 * @param attachment The attachment to validate
 * @returns An object with validation result and reason if failed
 */
export const validateAttachment = (attachment: {
  filename?: string;
  content_type: string;
}): { isValid: boolean; reason?: string } => {
  // Check if content type is allowed
  if (!isContentTypeAllowed(attachment.content_type)) {
    return {
      isValid: false,
      reason: `Content type '${attachment.content_type}' is not allowed`
    };
  }
  
  // Check if file extension matches content type
  if (attachment.filename && !isExtensionMatchingContentType(attachment.filename, attachment.content_type)) {
    return {
      isValid: false,
      reason: `File extension for '${attachment.filename}' does not match content type '${attachment.content_type}'`
    };
  }
  
  // All checks passed
  return { isValid: true };
};

/**
 * Sanitizes a filename to make it safer
 * @param filename The filename to sanitize
 * @returns A sanitized version of the filename
 */
export const sanitizeFilename = (filename: string): string => {
  // Replace potentially dangerous characters
  return filename
    .replace(/[\/\\]/g, '_') // Replace slashes with underscores
    .replace(/[^a-zA-Z0-9_\-. ]/g, '') // Only allow alphanumeric, underscore, hyphen, period, and space
    .trim();
};
