import { SMTPServer, SMTPServerSession, SMTPServerDataStream } from 'smtp-server';
import { simpleParser } from 'mailparser';
import sanitizeHtml from 'sanitize-html';
import { InboxModel } from '../models/Inbox';
import { EmailModel, Email, EmailAttachment } from '../models/Email';
import smtpConfig from '../config/smtp';
import logger from '../utils/logger';
import { validateAttachment, sanitizeFilename } from '../utils/attachmentValidator';

// Create a unique ID for each SMTP session for logging
let sessionCounter = 0;

/**
 * Initialize and start the SMTP server
 */
export const initSmtpServer = (): SMTPServer => {
  // Create SMTP server instance
  const server = new SMTPServer({
    // Connection settings
    secure: smtpConfig.secure,
    name: smtpConfig.host,
    size: smtpConfig.size,
    maxClients: smtpConfig.maxClients,

    // Allow authentication to be optional
    authOptional: smtpConfig.authOptional,

    // Disable DNS validation in development
    disabledCommands: smtpConfig.disableDnsValidation ? ['STARTTLS'] : [],

    // Handle new connection
    onConnect(session: SMTPServerSession, callback: (err?: Error | null) => void) {
      // Assign a unique session ID for tracking
      (session as any).id = `smtp-${++sessionCounter}`;

      logger.info(`[SMTP] New connection from ${session.remoteAddress}`, (session as any).id);

      // Accept the connection
      callback();
    },

    // Handle RCPT TO command (recipient validation)
    onRcptTo(address: { address: string }, session: SMTPServerSession, callback: (err?: Error | null) => void) {
      const recipient = address.address.toLowerCase();

      logger.info(`[SMTP] Recipient: ${recipient}`, (session as any).id);

      // Extract domain from recipient
      const domain = recipient.split('@')[1];

      // Check if domain is allowed
      if (smtpConfig.allowedDomains.length > 0 && !smtpConfig.allowedDomains.includes(domain)) {
        logger.warn(`[SMTP] Domain not allowed: ${domain}`, (session as any).id);
        return callback(new Error(`Domain ${domain} not allowed`));
      }

      // Accept the recipient
      callback();
    },

    // Handle incoming message
    async onData(stream: SMTPServerDataStream, session: SMTPServerSession, callback: (err?: Error | null) => void) {
      try {
        logger.info(`[SMTP] Processing message data`, (session as any).id);

        // Parse the email
        const parsedMail = await simpleParser(stream);

        // Get recipient address
        let recipient = '';

        // Try to extract email from the raw headers
        const rcptTo = parsedMail.headers.get('to');
        if (typeof rcptTo === 'string') {
          const emailMatch = rcptTo.match(/[^\s<]+@[^\s>]+/);
          if (emailMatch) {
            recipient = emailMatch[0];
          }
        }

        recipient = recipient.toLowerCase();

        // Extract inbox name from recipient
        const inboxName = recipient.split('@')[0];
        const domain = recipient.split('@')[1];

        logger.info(`[SMTP] Message for inbox: ${inboxName}@${domain}`, (session as any).id);

        // Find the inbox
        const inbox = await InboxModel.getByEmail(`${inboxName}@${domain}`);

        if (!inbox) {
          logger.warn(`[SMTP] Inbox not found: ${inboxName}@${domain}`, (session as any).id);
          return callback(new Error(`Inbox not found: ${inboxName}@${domain}`));
        }

        // Check if inbox is expired
        if (inbox.expires_at && new Date(inbox.expires_at) < new Date()) {
          logger.warn(`[SMTP] Inbox expired: ${inboxName}@${domain}`, (session as any).id);
          return callback(new Error(`Inbox expired: ${inboxName}@${domain}`));
        }

        // Sanitize HTML content with strict security settings
        const htmlContent = parsedMail.html
          ? sanitizeHtml(parsedMail.html.toString(), {
              // Only allow safe tags - no img tags to prevent tracking pixels
              allowedTags: [
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'p', 'a', 'ul', 'ol',
                'nl', 'li', 'b', 'i', 'strong', 'em', 'strike', 'code', 'hr', 'br', 'div',
                'table', 'thead', 'caption', 'tbody', 'tr', 'th', 'td', 'pre', 'span'
              ],
              allowedAttributes: {
                // Restrict attributes to minimum necessary
                a: ['href', 'rel', 'target'],
                span: ['style'],
                div: ['style'],
                p: ['style'],
                table: ['style'],
                tr: ['style'],
                td: ['style'],
                th: ['style']
              },
              // Add rel="noopener noreferrer" to all links
              transformTags: {
                'a': (tagName, attribs) => {
                  return {
                    tagName,
                    attribs: {
                      ...attribs,
                      rel: 'noopener noreferrer',
                      target: '_blank'
                    }
                  };
                }
              },
              // Only allow safe URL schemes
              allowedSchemes: ['https', 'mailto', 'tel'],
              // Disable script contents
              // Note: allowedScriptContents is a custom option we're adding
              // Add a hook to log potential XSS attempts
              disallowedTagsMode: 'discard',
              nonTextTags: ['style', 'script', 'textarea', 'option', 'noscript', 'svg', 'iframe'],
            })
          : undefined;

        // Store original HTML content for debugging if needed
        const originalHtml = parsedMail.html ? parsedMail.html.toString() : undefined;
        if (originalHtml && originalHtml.includes('<script') || originalHtml?.includes('javascript:')) {
          logger.warn(`[SMTP] Potential XSS attempt detected in email to ${recipient}`, (session as any).id);
        }

        // Prepare email data
        const email: Email = {
          inbox_id: inbox.id!,
          from_address: parsedMail.from?.text || '',
          from_name: parsedMail.from?.value?.[0]?.name,
          to_address: recipient,
          subject: parsedMail.subject,
          text_content: parsedMail.text,
          html_content: htmlContent,
          headers: Object.fromEntries(parsedMail.headers.entries()),
          has_attachments: parsedMail.attachments.length > 0,
          is_deleted: false,
        };

        // Process attachments if any
        if (parsedMail.attachments.length > 0) {
          // Filter attachments based on security rules
          const safeAttachments: EmailAttachment[] = [];

          for (const attachment of parsedMail.attachments) {
            // Sanitize filename
            const sanitizedFilename = sanitizeFilename(attachment.filename || 'unnamed-attachment');

            // Validate attachment
            const validation = validateAttachment({
              filename: sanitizedFilename,
              content_type: attachment.contentType || 'application/octet-stream'
            });

            if (validation.isValid) {
              // Add to safe attachments
              safeAttachments.push({
                email_id: '', // Will be set after email is created
                filename: sanitizedFilename,
                content_type: attachment.contentType || 'application/octet-stream',
                size: attachment.size || 0,
                content_id: attachment.contentId,
                is_inline: attachment.contentDisposition === 'inline',
              } as EmailAttachment);
            } else {
              // Log blocked attachment
              logger.warn(
                `[SMTP] Blocked attachment '${sanitizedFilename}' (${attachment.contentType}): ${validation.reason}`,
                (session as any).id
              );
            }
          }

          // Update email with filtered attachments
          email.attachments = safeAttachments;
          email.has_attachments = safeAttachments.length > 0;

          // Log attachment filtering results
          if (parsedMail.attachments.length !== safeAttachments.length) {
            logger.info(
              `[SMTP] Filtered attachments: ${safeAttachments.length} of ${parsedMail.attachments.length} allowed`,
              (session as any).id
            );
          }
        }

        // Save the email to database
        await EmailModel.create(email);

        logger.info(`[SMTP] Email saved successfully for ${inboxName}@${domain}`, (session as any).id);

        // Accept the message
        callback();
      } catch (error) {
        logger.error(`[SMTP] Error processing message: ${(error as Error).message}`, error as Error, (session as any).id);
        callback(new Error('Error processing message'));
      }
    },

    // Enable logging
    logger: smtpConfig.logger,
  });

  // Start the server
  server.listen(smtpConfig.port, smtpConfig.host);

  logger.info(`SMTP server listening on ${smtpConfig.host}:${smtpConfig.port}`);

  return server;
};

// Export a function to close the server
export const closeSmtpServer = (server: SMTPServer): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      server.close();
      logger.info('[SMTP] Server closed successfully');
      resolve();
    } catch (error) {
      logger.error(`[SMTP] Error closing server: ${(error as Error).message}`, error as Error);
      reject(error);
    }
  });
};
