import logger from './logger';

/**
 * Simple spam detection based on common patterns
 * Returns a score between 0 (not spam) and 1 (definitely spam)
 */
export const detectSpam = (
  from: string,
  subject: string,
  textContent: string,
  htmlContent: string
): number => {
  try {
    let score = 0;
    const maxScore = 10;

    // Combine all content for analysis
    const allContent = `${from} ${subject} ${textContent} ${htmlContent}`.toLowerCase();

    // Check for common spam keywords
    const spamKeywords = [
      'viagra', 'cialis', 'enlargement', 'lottery', 'winner', 'won', 'prize',
      'million dollars', 'nigerian', 'inheritance', 'bank transfer', 'wire transfer',
      'pharmacy', 'discount', 'free offer', 'limited time', 'act now', 'urgent',
      'cryptocurrency', 'bitcoin', 'investment opportunity', 'make money fast',
      'work from home', 'earn extra cash', 'no experience needed', 'get rich',
      'lose weight', 'miracle cure', 'secret revealed', 'guaranteed'
    ];

    // Check for spam keywords
    for (const keyword of spamKeywords) {
      if (allContent.includes(keyword)) {
        score += 0.5;
      }
    }

    // Check for excessive capitalization in subject
    if (subject && subject.toUpperCase() === subject && subject.length > 10) {
      score += 1;
    }

    // Check for excessive exclamation marks
    const exclamationCount = (allContent.match(/!/g) || []).length;
    if (exclamationCount > 3) {
      score += 0.5;
    }

    // Check for suspicious TLDs in sender address
    const suspiciousTlds = ['.xyz', '.top', '.club', '.stream', '.bid', '.review'];
    for (const tld of suspiciousTlds) {
      if (from.toLowerCase().endsWith(tld)) {
        score += 1;
      }
    }

    // Check for HTML with hidden content
    if (htmlContent) {
      if (htmlContent.includes('display:none') || htmlContent.includes('visibility:hidden')) {
        score += 1;
      }

      // Check for excessive use of colors
      const colorCount = (htmlContent.match(/color:/g) || []).length;
      if (colorCount > 10) {
        score += 0.5;
      }
    }

    // Normalize score to be between 0 and 1
    return Math.min(score / maxScore, 1);
  } catch (error: any) {
    logger.error('Error in spam detection:', new Error(error?.message || 'Unknown error'));
    return 0; // Default to not spam in case of error
  }
};
