import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { AppError } from './errorHandler';
import logger from '../utils/logger';

// Validation options
const validationOptions = {
  abortEarly: false, // Include all errors
  allowUnknown: true, // Ignore unknown props
  stripUnknown: true, // Remove unknown props
};

// Validation middleware factory
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Ensure req.body exists
      if (!req.body) {
        req.body = {};
      }

      // Normalize request body for case-insensitive parameter names
      const normalizedBody: Record<string, any> = {};

      // Process both body and query parameters
      const processParams = (source: Record<string, any>) => {
        Object.keys(source).forEach(key => {
          // Convert parameter name to lowercase for case-insensitive comparison
          const lowerKey = key.toLowerCase();
          normalizedBody[lowerKey] = source[key];
          // Also keep the original key to avoid breaking existing code
          normalizedBody[key] = source[key];
        });
      };

      // Process body parameters
      processParams(req.body);

      // Also check query parameters for POST requests
      if (req.method === 'POST' && req.query) {
        processParams(req.query as Record<string, any>);
      }

      // Log normalized body in development
      if (process.env.NODE_ENV !== 'production') {
        logger.debug('Normalized request body: ' + JSON.stringify(normalizedBody));
      }

      // Validate request against schema
      const { error, value } = schema.validate(normalizedBody, validationOptions);

      if (error) {
        // Format validation errors
        const errorMessage = error.details
          .map((detail) => detail.message)
          .join(', ');

        // Return validation error
        return next(new AppError(`Validation error: ${errorMessage}`, 400));
      }

      // Replace req.body with validated value
      req.body = value;
      return next();
    } catch (err) {
      // Log the actual error
      logger.error('Validation error:', err as Error);
      // Handle any unexpected errors
      return next(new AppError('Validation failed due to server error', 500));
    }
  };
};

// Query validation middleware factory
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Ensure req.query exists
      if (!req.query) {
        req.query = {};
      }

      // Normalize query parameters for case-insensitive parameter names
      const normalizedQuery: Record<string, any> = {};

      // Process query parameters
      Object.keys(req.query).forEach(key => {
        // Convert parameter name to lowercase for case-insensitive comparison
        const lowerKey = key.toLowerCase();
        normalizedQuery[lowerKey] = req.query[key];
        // Also keep the original key to avoid breaking existing code
        normalizedQuery[key] = req.query[key];
      });

      // Log normalized query in development
      if (process.env.NODE_ENV !== 'production') {
        logger.debug('Normalized query parameters: ' + JSON.stringify(normalizedQuery));
      }

      // Validate query parameters against schema
      const { error, value } = schema.validate(normalizedQuery, validationOptions);

      if (error) {
        // Format validation errors
        const errorMessage = error.details
          .map((detail) => detail.message)
          .join(', ');

        // Return validation error
        return next(new AppError(`Validation error: ${errorMessage}`, 400));
      }

      // Instead of replacing req.query, copy validated values to it
      // Store validated values in a custom property
      (req as any).validatedQuery = value;
      return next();
    } catch (err) {
      // Log the actual error
      logger.error('Query validation error:', err as Error);
      // Handle any unexpected errors
      return next(new AppError('Query validation failed due to server error', 500));
    }
  };
};

// Params validation middleware factory
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Ensure req.params exists
      if (!req.params) {
        req.params = {};
      }

      // Validate URL parameters against schema
      const { error, value } = schema.validate(req.params, validationOptions);

      if (error) {
        // Format validation errors
        const errorMessage = error.details
          .map((detail) => detail.message)
          .join(', ');

        // Return validation error
        return next(new AppError(`Validation error: ${errorMessage}`, 400));
      }

      // Replace req.params with validated value
      req.params = value;
      return next();
    } catch (err) {
      // Handle any unexpected errors
      return next(new AppError('Parameter validation failed due to server error', 500));
    }
  };
};
