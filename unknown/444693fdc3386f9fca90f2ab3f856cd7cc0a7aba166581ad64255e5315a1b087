import express from 'express';
import * as apiKeyController from '../controllers/apiKeyController';
import { apiKeyAuth } from '../middlewares/apiKeyAuth';

const router = express.Router();

// POST /api/keys - Create a new API key
router.post('/', apiKeyAuth, apiKeyController.createApiKey);

// GET /api/keys - List all API keys
router.get('/', apiKeyAuth, apiKeyController.listApiKeys);

// DELETE /api/keys/:id - Revoke an API key
router.delete('/:id', apiKeyAuth, apiKeyController.revokeApiKey);

export default router;
