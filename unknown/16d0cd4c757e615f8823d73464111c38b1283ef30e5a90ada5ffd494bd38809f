import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * Middleware to generate and attach a request ID to each request
 * This helps with request tracing and debugging
 */
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Skip for health check endpoints
  if (req.path === '/health' || req.path === '/healthz') {
    return next();
  }

  try {
    // Generate a unique request ID
    const requestId = uuidv4().toUpperCase();

    // Attach it to the request object for use in controllers
    (req as any).requestId = requestId;

    // Add it as a response header (only if headers haven't been sent yet)
    if (!res.headersSent) {
      res.setHeader('X-Request-ID', requestId);
    }
  } catch (error) {
    // Log error but don't fail the request
    console.error('Error in request ID middleware:', error);
  }

  next();
};
