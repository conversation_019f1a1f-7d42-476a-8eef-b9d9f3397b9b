import express from 'express';
import * as domainController from '../controllers/domainController';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { rapidApiRateLimiter } from '../middlewares/rapidApiRateLimiter';
import { subscriptionRateLimiter } from '../middlewares/subscriptionRateLimiter';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { cacheMiddleware } from '../middlewares/cacheMiddleware';

const router = express.Router();

// Apply authentication first, then subscription-aware rate limiting
router.use(rapidApiAuth);

// Use subscription-aware rate limiter for domains endpoint
router.use(subscriptionRateLimiter);

// GET /api/domains - Get all domains
// PHASE 2: Optimized cache middleware with improved key generation
router.get('/', cacheMiddleware(604800, req => {
  // PHASE 2: Improved cache key generation including query parameters
  const rapidApiKey = req.headers['x-rapidapi-key'] || 'public';
  const queryString = Object.keys(req.query).length > 0 ? JSON.stringify(req.query) : '';
  return `domains:all:${rapidApiKey}:${queryString}`;
}), domainController.getDomains);

export default router;
