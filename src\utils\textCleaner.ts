import logger from './logger';

/**
 * Text cleaning utility for processing message content
 * Removes duplicates, unnecessary whitespace, and metadata while preserving meaning
 */

/**
 * Clean text content by removing duplicates, metadata, and unnecessary formatting
 * @param text - The raw text content to clean
 * @returns Cleaned text content
 */
export const cleanTextContent = (text: string | null | undefined): string | null => {
  // Return null for null/undefined input
  if (!text) {
    return null;
  }

  try {
    // Convert to string and trim initial whitespace
    let cleanedText = text.toString().trim();

    // If empty after trimming, return null
    if (!cleanedText) {
      return null;
    }

    // Step 1: Remove common email headers and metadata patterns
    cleanedText = removeEmailMetadata(cleanedText);

    // Step 2: Remove duplicate sentences/phrases
    cleanedText = removeDuplicateContent(cleanedText);

    // Step 3: Clean up whitespace and formatting
    cleanedText = normalizeWhitespace(cleanedText);

    // Step 4: Remove empty lines and excessive line breaks
    cleanedText = removeExcessiveLineBreaks(cleanedText);

    // Final trim and cleanup
    cleanedText = cleanedText.trim();

    // Remove any remaining standalone punctuation or meaningless content (but preserve sentence-ending periods)
    if (cleanedText.match(/^[;,\s]+$/)) {
      return null; // Only punctuation and whitespace
    }

    // Remove leading punctuation but preserve trailing periods for sentences
    cleanedText = cleanedText.replace(/^[;,\s]+/, '');

    // Return null if nothing meaningful remains
    return cleanedText.length > 0 ? cleanedText : null;

  } catch (error: any) {
    logger.error('Error cleaning text content:', new Error(error?.message || 'Unknown error'));
    // Return original text if cleaning fails
    return text;
  }
};

/**
 * Remove common email metadata and headers from text
 * @param text - Input text
 * @returns Text with metadata removed
 */
const removeEmailMetadata = (text: string): string => {
  let cleaned = text;

  // Remove Content-Type headers and similar metadata (more precise pattern)
  // Match Content-Type followed by its value, but stop at sentence boundaries or new content
  cleaned = cleaned.replace(/\s*Content-Type:\s*[^;\s]+(?:;\s*[^=\s]+="?[^"\s]*"?)?\s*/gi, ' ');

  // Remove other common email headers
  const headerPatterns = [
    /Content-Transfer-Encoding:\s*[^\n\r]+\s*/gi,
    /Content-Disposition:\s*[^\n\r]+\s*/gi,
    /Content-ID:\s*[^\n\r]+\s*/gi,
    /MIME-Version:\s*[^\n\r]+\s*/gi,
    /X-[^:\n\r]+:\s*[^\n\r]+\s*/gi, // X-headers
    /Message-ID:\s*[^\n\r]+\s*/gi,
    /Date:\s*[^\n\r]+\s*/gi,
    /From:\s*[^\n\r]+\s*/gi,
    /To:\s*[^\n\r]+\s*/gi,
    /Subject:\s*[^\n\r]+\s*/gi,
    /Reply-To:\s*[^\n\r]+\s*/gi,
    /Return-Path:\s*[^\n\r]+\s*/gi,
    /Received:\s*[^\n\r]+\s*/gi,
  ];

  headerPatterns.forEach(pattern => {
    cleaned = cleaned.replace(pattern, '');
  });

  // Remove charset specifications (standalone)
  cleaned = cleaned.replace(/\s*charset\s*=\s*"?[^"\s;]+"?\s*/gi, ' ');

  // Remove boundary markers
  cleaned = cleaned.replace(/--[a-zA-Z0-9_-]+/g, '');

  // Clean up any leftover semicolons and extra spaces
  cleaned = cleaned.replace(/\s*;\s*/g, ' ');
  cleaned = cleaned.replace(/\s+/g, ' ');

  return cleaned;
};

/**
 * Remove duplicate sentences and phrases from text
 * @param text - Input text
 * @returns Text with duplicates removed
 */
const removeDuplicateContent = (text: string): string => {
  // Split text into sentences, but be very careful to preserve original structure
  // Use a simple approach that splits on sentence endings
  const sentencePattern = /([.!?]+)/;
  const parts = text.split(sentencePattern);

  if (parts.length <= 2) {
    return text; // Only one sentence or less
  }

  // Reconstruct sentences with their punctuation and spacing
  const sentences = [];
  for (let i = 0; i < parts.length - 1; i += 2) {
    const sentenceText = parts[i];
    const punctuation = parts[i + 1] || '';

    if (sentenceText && sentenceText.trim().length > 0) {
      sentences.push({
        original: sentenceText + punctuation,
        normalized: sentenceText.trim().toLowerCase().replace(/\s+/g, ' ')
      });
    }
  }

  // Check if there are any duplicates
  const seenNormalized = new Set();
  const uniqueSentences = [];
  let foundDuplicates = false;

  for (const sentence of sentences) {
    if (!seenNormalized.has(sentence.normalized)) {
      seenNormalized.add(sentence.normalized);
      uniqueSentences.push(sentence.original);
    } else {
      foundDuplicates = true;
    }
  }

  // Only modify the text if we actually found duplicates
  if (foundDuplicates) {
    return uniqueSentences.join('').trim();
  }

  return text;
};

/**
 * Normalize whitespace in text
 * @param text - Input text
 * @returns Text with normalized whitespace
 */
const normalizeWhitespace = (text: string): string => {
  return text
    // Replace multiple spaces with single space
    .replace(/[ \t]+/g, ' ')
    // Replace multiple tabs with single space
    .replace(/\t+/g, ' ')
    // Normalize line endings
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n');
};

/**
 * Remove excessive line breaks and empty lines
 * @param text - Input text
 * @returns Text with cleaned line breaks
 */
const removeExcessiveLineBreaks = (text: string): string => {
  return text
    // Replace multiple consecutive newlines with maximum of 2
    .replace(/\n{3,}/g, '\n\n')
    // Remove trailing whitespace from lines
    .replace(/[ \t]+$/gm, '')
    // Remove empty lines that contain only whitespace, but preserve double line breaks
    .replace(/\n\s*\n\s*\n/g, '\n\n');
};

/**
 * Check if text appears to contain duplicate content
 * @param text - Text to analyze
 * @returns True if duplicates are detected
 */
export const hasDuplicateContent = (text: string | null | undefined): boolean => {
  if (!text || text.length < 20) {
    return false;
  }

  try {
    const sentences = text
      .split(/[.!?]+/)
      .map(s => s.trim().toLowerCase())
      .filter(s => s.length > 5); // Only check sentences with meaningful length

    if (sentences.length <= 1) {
      return false;
    }

    const uniqueSentences = new Set(sentences);
    return uniqueSentences.size < sentences.length;

  } catch (error: any) {
    logger.error('Error checking for duplicate content:', new Error(error?.message || 'Unknown error'));
    return false;
  }
};

/**
 * Get statistics about text cleaning operation
 * @param originalText - Original text before cleaning
 * @param cleanedText - Text after cleaning
 * @returns Statistics object
 */
export const getCleaningStats = (
  originalText: string | null | undefined,
  cleanedText: string | null | undefined
): {
  originalLength: number;
  cleanedLength: number;
  reductionPercentage: number;
  hadDuplicates: boolean;
} => {
  const origLen = originalText?.length || 0;
  const cleanLen = cleanedText?.length || 0;
  const reduction = origLen > 0 ? ((origLen - cleanLen) / origLen) * 100 : 0;

  return {
    originalLength: origLen,
    cleanedLength: cleanLen,
    reductionPercentage: Math.round(reduction * 100) / 100,
    hadDuplicates: hasDuplicateContent(originalText)
  };
};
