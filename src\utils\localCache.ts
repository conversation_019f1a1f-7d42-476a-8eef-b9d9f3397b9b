/**
 * Simple in-memory cache implementation to reduce Redis latency
 * This is especially useful for static data like domains
 */

import logger from './logger';

interface CacheEntry<T> {
  value: T;
  expiry: number; // Timestamp when this entry expires
}

class LocalCache {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private readonly cleanupInterval: number = 60 * 1000; // 1 minute
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor() {
    // Start the cleanup timer
    this.startCleanupTimer();
  }

  /**
   * Set a value in the cache
   * @param key - Cache key
   * @param value - Value to cache
   * @param ttlSeconds - Time to live in seconds
   */
  set<T>(key: string, value: T, ttlSeconds: number): void {
    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { value, expiry });
    logger.debug(`LocalCache: Set key ${key} with TTL ${ttlSeconds}s`);
  }

  /**
   * Get a value from the cache
   * @param key - Cache key
   * @returns The cached value or null if not found or expired
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    // If entry doesn't exist or is expired
    if (!entry || entry.expiry < Date.now()) {
      if (entry) {
        // Remove expired entry
        this.cache.delete(key);
        logger.debug(`LocalCache: Key ${key} expired and removed`);
      }
      return null;
    }
    
    logger.debug(`LocalCache: Cache hit for key ${key}`);
    return entry.value as T;
  }

  /**
   * Delete a value from the cache
   * @param key - Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
    logger.debug(`LocalCache: Deleted key ${key}`);
  }

  /**
   * Clear all entries from the cache
   */
  clear(): void {
    this.cache.clear();
    logger.debug('LocalCache: Cleared all entries');
  }

  /**
   * Get the number of entries in the cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Start the cleanup timer to remove expired entries
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiry < now) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      logger.debug(`LocalCache: Cleaned up ${removedCount} expired entries`);
    }
  }

  /**
   * Stop the cleanup timer
   */
  stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
}

// Create a singleton instance
const localCache = new LocalCache();

export default localCache;
