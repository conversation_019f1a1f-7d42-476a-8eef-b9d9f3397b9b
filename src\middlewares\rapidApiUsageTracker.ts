import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { RapidApiUsageModel } from '../models/RapidApiUsage';

/**
 * Simplified middleware to track RapidAPI usage
 * Optimized for performance with minimal overhead
 */
export const rapidApiUsageTracker = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Only process if this is a RapidAPI request
    if (!(req as any).rapidApi) {
      return next();
    }

    // Skip tracking for high-traffic endpoints
    if (req.path === '/api/domains' || req.path === '/healthz') {
      return next();
    }

    // Extract minimal RapidAPI information
    const user = (req as any).rapidApi.user || 'unknown';

    // Track usage asynchronously without blocking the request
    setImmediate(() => {
      try {
        RapidApiUsageModel.trackUsage({
          user,
          endpoint: req.path,
          method: req.method,
          rateLimitLimit: 0,
          rateLimitRemaining: 0,
          rateLimitReset: 0,
          planType: 'UNKNOWN'
        }).catch(() => {}); // Ignore errors
      } catch (e) {
        // Ignore errors
      }
    });

    next();
  } catch (error) {
    // Log error but don't fail the request
    logger.error('Error tracking RapidAPI usage:', error instanceof Error ? error : new Error(String(error)));
    next();
  }
};

export default rapidApiUsageTracker;
