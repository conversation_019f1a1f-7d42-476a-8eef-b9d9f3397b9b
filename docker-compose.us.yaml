services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=us-1
      
      # US PRIMARY SERVER CONFIGURATION
      - REGION=us
      - DB_ROLE=primary
      - SERVER_LOCATION=us-east
      
      # Connect to PostgreSQL on VPS host (US Primary Database)
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      
      # Database connection pool settings (Primary server - higher limits)
      - PG_MAX_CONNECTIONS=${PG_MAX_CONNECTIONS:-30}
      - PG_MIN_CONNECTIONS=${PG_MIN_CONNECTIONS:-10}
      - PG_IDLE_TIMEOUT=${PG_IDLE_TIMEOUT:-20000}
      - PG_CONNECTION_TIMEOUT=${PG_CONNECTION_TIMEOUT:-5000}
      - PG_STATEMENT_TIMEOUT=${PG_STATEMENT_TIMEOUT:-30000}
      
      # Read/Write Pool Configuration (US Primary handles both)
      - WRITE_POOL_HOST=host.docker.internal
      - WRITE_POOL_PORT=5432
      - WRITE_POOL_DATABASE=tempfly_app
      - WRITE_POOL_USER=postgres
      - WRITE_POOL_PASSWORD=4wyWCAAk92hkGUhdh7
      - READ_POOL_HOST=host.docker.internal
      - READ_POOL_PORT=5432
      - READ_POOL_DATABASE=tempfly_app
      - READ_POOL_USER=postgres
      - READ_POOL_PASSWORD=4wyWCAAk92hkGUhdh7
      
      # Inbox cleanup configuration (PRIMARY ONLY)
      - INBOX_CLEANUP_INTERVAL=${INBOX_CLEANUP_INTERVAL:-3600000}  # 1 hour
      - ENABLE_AUTOMATED_CLEANUP=true
      
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      
      # Application settings
      - CONNECTION_WARMUP_INTERVAL=${CONNECTION_WARMUP_INTERVAL:-45000}
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-200}
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-1000}
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      
      # Admin and security settings
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      
      # Feature flags
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - ENABLE_PERFORMANCE_MONITORING=${ENABLE_PERFORMANCE_MONITORING:-true}
      - ENABLE_REQUEST_LOGGING=${ENABLE_REQUEST_LOGGING:-true}
      - ENABLE_SMTP_SERVER=${ENABLE_SMTP_SERVER:-false}
      
      # Cache settings
      - FORCE_CACHE=${FORCE_CACHE:-true}
      
      # User limits
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      
      # Logging configuration
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-10}
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-5}
      
      # Rate limiting (Primary server - more permissive)
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-1000}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-60000}
      
      # Health monitoring
      - HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-60000}
      - DB_HEALTH_CHECK_TIMEOUT=${DB_HEALTH_CHECK_TIMEOUT:-5000}
      
      # SMTP Server configuration (if enabled)
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_HOST_1=${SMTP_HOST_1}
      - SMTP_HOST_2=${SMTP_HOST_2}
      - SMTP_PORT=${SMTP_PORT:-25}
      - SMTP_SECURE=${SMTP_SECURE:-false}
      - SMTP_AUTH_USER=${SMTP_AUTH_USER}
      - SMTP_AUTH_PASS=${SMTP_AUTH_PASS}
      
      # Graceful shutdown
      - GRACEFUL_SHUTDOWN_TIMEOUT=${GRACEFUL_SHUTDOWN_TIMEOUT:-30000}
      
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1g
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
