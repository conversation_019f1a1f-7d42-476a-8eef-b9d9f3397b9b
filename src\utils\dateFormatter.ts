/**
 * Format a date to YYYY-MM-DD format
 * @param date - Date to format (Date object or ISO string)
 * @returns Formatted date string in YYYY-MM-DD format, or "9999-12-31" for null dates (long-term inboxes)
 */
export const formatDate = (date: Date | string | null | undefined): string => {
  // Fast path for null/undefined dates
  if (!date) {
    return "9999-12-31";
  }

  // Optimize date conversion
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Fast path for invalid dates
  if (isNaN(dateObj.getTime())) {
    return "9999-12-31";
  }

  // Format to YYYY-MM-DD - optimized to avoid unnecessary string operations
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Format a time to HH:MM format
 * @param date - Date to format (Date object or ISO string)
 * @returns Formatted time string in HH:MM format, or "23:59" for null dates (long-term inboxes)
 */
export const formatTime = (date: Date | string | null | undefined): string => {
  // Fast path for null/undefined dates
  if (!date) {
    return "23:59";
  }

  // Optimize date conversion
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Fast path for invalid dates
  if (isNaN(dateObj.getTime())) {
    return "23:59";
  }

  // Format to HH:MM - optimized to avoid unnecessary string operations
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');

  return `${hours}:${minutes}`;
};
