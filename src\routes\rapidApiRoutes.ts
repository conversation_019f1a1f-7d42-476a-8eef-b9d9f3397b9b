import express from 'express';
import * as rapidApiController from '../controllers/rapidApiController';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { generalRateLimiter } from '../middlewares/rateLimiter';

const router = express.Router();

// Apply RapidAPI authentication to all routes
router.use(rapidApiAuth);
router.use(generalRateLimiter);

// GET /api/rapidapi/usage - Get usage for the current RapidAPI user
router.get('/usage', rapidApiController.getUserUsage);

export default router;
