import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import logger from '../utils/logger';
import auditLogger from '../utils/auditLogger';
import { ApiKeyModel } from '../models/ApiKey';

// Note: The Request interface is now defined in src/types/express/index.d.ts

/**
 * Simplified RapidAPI authentication middleware
 * Validates the RapidAPI Proxy Secret and extracts minimal required information
 */
export const rapidApiAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    // Log all headers in development mode for debugging
    if (process.env.NODE_ENV !== 'production' && process.env.DEBUG_AUTH === 'true') {
      logger.debug('Auth headers:', JSON.stringify({
        'x-rapidapi-proxy-secret': req.headers['x-rapidapi-proxy-secret'] ? 'present' : 'missing',
        'X-RapidAPI-Proxy-Secret': req.headers['X-RapidAPI-Proxy-Secret'] ? 'present' : 'missing',
        'x-rapidapi-key': req.headers['x-rapidapi-key'] ? 'present' : 'missing',
        'X-RapidAPI-Key': req.headers['X-RapidAPI-Key'] ? 'present' : 'missing',
        'x-rapidapi-host': req.headers['x-rapidapi-host'],
        'X-RapidAPI-Host': req.headers['X-RapidAPI-Host'],
        'x-api-key': req.headers['x-api-key'] ? 'present' : 'missing',
        // Log free user headers for debugging
        'x-ratelimit-rapid-free-plans-hard-limit-limit': req.headers['x-ratelimit-rapid-free-plans-hard-limit-limit'] || 'missing',
        'x-ratelimit-rapid-free-plans-hard-limit-remaining': req.headers['x-ratelimit-rapid-free-plans-hard-limit-remaining'] || 'missing',
        'x-ratelimit-rapid-free-plans-hard-limit-reset': req.headers['x-ratelimit-rapid-free-plans-hard-limit-reset'] || 'missing'
      }));
    }

    // Get RapidAPI Proxy Secret - this is the critical header
    const rapidApiProxySecret = (req.headers['x-rapidapi-proxy-secret'] ||
                               req.headers['X-RapidAPI-Proxy-Secret'] || '') as string;

    // Extract minimal required information
    // FIXED: Properly capture the actual RapidAPI username
    const rapidApiUser = (req.headers['x-rapidapi-user'] ||
                        req.headers['X-RapidAPI-User'] ||
                        req.headers['x-rapidapi-username'] ||
                        req.headers['X-RapidAPI-Username'] || 'unknown') as string;

    // Get RapidAPI key
    const rapidApiKey = (req.headers['x-rapidapi-key'] ||
                       req.headers['X-RapidAPI-Key'] || '') as string;

    // Get RapidAPI host
    const rapidApiHost = (req.headers['x-rapidapi-host'] ||
                        req.headers['X-RapidAPI-Host'] || '') as string;

    // Debug log headers only in development environment
    if (process.env.NODE_ENV !== 'production' && process.env.DEBUG_AUTH === 'true') {
      logger.debug(`RapidAPI Proxy Secret present: ${!!rapidApiProxySecret}`);
      logger.debug(`RapidAPI Key present: ${!!rapidApiKey}`);
      logger.debug(`RapidAPI Host: ${rapidApiHost}`);
    }

    // OPTION 1: Check for valid Proxy Secret (preferred method)
    if (rapidApiProxySecret && process.env.RAPIDAPI_PROXY_SECRET &&
        rapidApiProxySecret === process.env.RAPIDAPI_PROXY_SECRET) {

      // This is a verified RapidAPI request with the correct proxy secret
      if (process.env.NODE_ENV !== 'production') {
        logger.info('Authenticated RapidAPI request via Proxy Secret');
      }

      // Extract rate limit information from headers for plan type detection
      const getHeader = (name: string): string => {
        const lowerName = name.toLowerCase();
        const headerKey = Object.keys(req.headers).find(
          key => key.toLowerCase() === lowerName
        );
        return headerKey ? (req.headers[headerKey] as string) : '';
      };

      // Get rate limit information
      const rateLimitLimit = parseInt(getHeader('x-ratelimit-requests-limit') || '0', 10);
      const rateLimitRemaining = parseInt(getHeader('x-ratelimit-requests-remaining') || '0', 10);
      const rateLimitReset = parseInt(getHeader('x-ratelimit-requests-reset') || '0', 10);

      // CORRECT FREE USER DETECTION: Use X-RapidAPI-Subscription header
      // According to RapidAPI official documentation, this header indicates the subscription type:
      // - BASIC (free plan)
      // - PRO, ULTRA, MEGA, CUSTOM (paid plans)
      const subscription = getHeader('x-rapidapi-subscription') || '';

      let planType = 'UNKNOWN';

      if (subscription) {
        const subLower = subscription.toLowerCase();
        if (subLower === 'basic') {
          planType = 'FREE';
        } else if (['pro', 'ultra', 'mega', 'custom'].includes(subLower)) {
          planType = 'PAID';
        } else {
          planType = 'UNKNOWN';
        }

        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`🔍 Plan type detected from X-RapidAPI-Subscription: "${subscription}" -> ${planType}`);
        }
      } else {
        // FALLBACK: If no subscription header, assume paid user for safety
        // This prevents accidentally limiting paid users
        planType = 'PAID';

        if (process.env.NODE_ENV !== 'production') {
          logger.debug('🔍 No X-RapidAPI-Subscription header found, defaulting to PAID plan for safety');
        }
      }

      // Log plan type detection for debugging (only in development)
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`🔍 Final plan type: ${planType} (from ${subscription ? 'X-RapidAPI-Subscription header' : 'fallback detection'})`);
        if (planType === 'FREE') {
          logger.debug('🚨 Free user detected - inbox limits will be enforced');
        }
      }

      // ALWAYS log free user detection for monitoring
      if (planType === 'FREE') {
        logger.info(`🚨 FREE USER DETECTED: ${rapidApiUser} (${rapidApiKey}) - inbox limits will be enforced`);
      }

      // Store RapidAPI information in request object with plan type
      // CRITICAL FIX: Always include usage object with plan type for free user detection
      // FIXED: Use actual RapidAPI key or generate a stable identifier based on user
      const effectiveKey = rapidApiKey || `proxy-auth-${rapidApiUser}`;

      (req as any).rapidApi = {
        key: effectiveKey,
        user: rapidApiUser,
        authenticated: true,
        usage: {
          limit: rateLimitLimit,
          remaining: rateLimitRemaining,
          reset: rateLimitReset,
          planType: planType
        }
      };

      return next();
    }

    // OPTION 2: Fall back to standard RapidAPI headers if enabled
    // This is a fallback for RapidAPI requests that don't have the Proxy Secret
    // Only use this if ALLOW_RAPIDAPI_KEY_AUTH is set to true
    if (process.env.ALLOW_RAPIDAPI_KEY_AUTH === 'true' &&
        rapidApiKey && rapidApiHost &&
        (rapidApiHost.includes('rapidapi.com') || rapidApiHost === 'tempfly2.p.rapidapi.com')) {

      logger.info(`Authenticated RapidAPI request via standard headers from host: ${rapidApiHost}`);

      // Extract rate limit information from headers for plan type detection
      const getHeader = (name: string): string => {
        const lowerName = name.toLowerCase();
        const headerKey = Object.keys(req.headers).find(
          key => key.toLowerCase() === lowerName
        );
        return headerKey ? (req.headers[headerKey] as string) : '';
      };

      // Get rate limit information
      const rateLimitLimit = parseInt(getHeader('x-ratelimit-requests-limit') || '0', 10);
      const rateLimitRemaining = parseInt(getHeader('x-ratelimit-requests-remaining') || '0', 10);
      const rateLimitReset = parseInt(getHeader('x-ratelimit-requests-reset') || '0', 10);

      // CORRECT FREE USER DETECTION: Use X-RapidAPI-Subscription header
      // According to RapidAPI official documentation, this header indicates the subscription type:
      // - BASIC (free plan)
      // - PRO, ULTRA, MEGA, CUSTOM (paid plans)
      const subscription = getHeader('x-rapidapi-subscription') || '';

      let planType = 'UNKNOWN';

      if (subscription) {
        const subLower = subscription.toLowerCase();
        if (subLower === 'basic') {
          planType = 'FREE';
        } else if (['pro', 'ultra', 'mega', 'custom'].includes(subLower)) {
          planType = 'PAID';
        } else {
          planType = 'UNKNOWN';
        }

        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`🔍 Plan type detected from X-RapidAPI-Subscription (fallback auth): "${subscription}" -> ${planType}`);
        }
      } else {
        // FALLBACK: If no subscription header, assume paid user for safety
        // This prevents accidentally limiting paid users
        planType = 'PAID';

        if (process.env.NODE_ENV !== 'production') {
          logger.debug('🔍 No X-RapidAPI-Subscription header found (fallback auth), defaulting to PAID plan for safety');
        }
      }

      // Log plan type detection for debugging (only in development)
      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Plan type detected (fallback auth): ${planType}`);
        if (planType === 'FREE') {
          logger.debug('Free user detected (fallback auth) - inbox limits will be enforced');
        }
      }

      // Store RapidAPI information in request object with plan type
      // CRITICAL FIX: Always include usage object with plan type for free user detection
      (req as any).rapidApi = {
        key: rapidApiKey,
        user: rapidApiUser || 'unknown',
        authenticated: true,
        usage: {
          limit: rateLimitLimit,
          remaining: rateLimitRemaining,
          reset: rateLimitReset,
          planType: planType
        }
      };

      return next();
    }

    // Log the missing Proxy Secret for debugging
    if (rapidApiKey && rapidApiHost &&
        (rapidApiHost.includes('rapidapi.com') || rapidApiHost === 'tempfly2.p.rapidapi.com')) {
      logger.warn(`Request with RapidAPI headers but missing valid Proxy Secret from host: ${rapidApiHost}`);
      // Do not authenticate as a RapidAPI request - fall through to API key authentication
    }

    // OPTION 3: If not a RapidAPI request, fall back to regular API key authentication
    // This allows your API to work both directly and through RapidAPI
    const apiKey = req.headers['x-api-key'] as string;

    if (!apiKey) {
      // Log failed authentication attempt
      auditLogger.logSecurityEvent(req, 'authentication', { message: 'API key missing' }, false);
      return next(new AppError('API key or RapidAPI authentication is required', 401));
    }

    // Validate API key with subscription information
    const apiKeyData = await ApiKeyModel.getByKeyWithSubscription(apiKey);

    // Check if API key exists and is active
    if (!apiKeyData) {
      // Log failed authentication attempt
      auditLogger.logSecurityEvent(req, 'authentication', { message: 'Invalid API key' }, false);
      return next(new AppError('Invalid API key', 401));
    }

    // Check if API key is expired
    if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
      // Log failed authentication attempt
      auditLogger.logSecurityEvent(req, 'authentication', { message: 'Expired API key', keyId: apiKeyData.id }, false);
      return next(new AppError('API key has expired', 401));
    }

    // Track API key usage
    await ApiKeyModel.trackUsage(apiKey);

    // Attach API key data to request
    req.apiKey = apiKeyData;

    // Log successful authentication
    auditLogger.logSecurityEvent(req, 'authentication', { keyId: apiKeyData.id }, true);

    // Continue to next middleware
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional RapidAPI authentication middleware
 * Will attach RapidAPI data to request if provided, but won't require it
 */
export const optionalRapidApiAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    // Get RapidAPI headers
    const rapidApiKey = (req.headers['x-rapidapi-key'] ||
                       req.headers['X-RapidAPI-Key'] || '') as string;

    const rapidApiHost = (req.headers['x-rapidapi-host'] ||
                        req.headers['X-RapidAPI-Host'] || '') as string;

    // Get the RapidAPI Proxy Secret
    const rapidApiProxySecret = (req.headers['x-rapidapi-proxy-secret'] ||
                               req.headers['X-RapidAPI-Proxy-Secret'] || '') as string;

    const rapidApiUser = (req.headers['x-rapidapi-user'] ||
                        req.headers['X-RapidAPI-User'] || '') as string;

    // Check for RapidAPI Proxy Secret first
    if (rapidApiProxySecret && process.env.RAPIDAPI_PROXY_SECRET &&
        rapidApiProxySecret === process.env.RAPIDAPI_PROXY_SECRET) {

      // Extract rate limit information from headers
      const getHeader = (name: string): string => {
        const lowerName = name.toLowerCase();
        const headerKey = Object.keys(req.headers).find(
          key => key.toLowerCase() === lowerName
        );
        return headerKey ? (req.headers[headerKey] as string) : '';
      };

      // Get rate limit information
      const rateLimitLimit = parseInt(getHeader('x-ratelimit-requests-limit') || '0', 10);
      const rateLimitRemaining = parseInt(getHeader('x-ratelimit-requests-remaining') || '0', 10);
      const rateLimitReset = parseInt(getHeader('x-ratelimit-requests-reset') || '0', 10);

      // CORRECT FREE USER DETECTION: Use X-RapidAPI-Subscription header
      // According to RapidAPI official documentation, this header indicates the subscription type:
      // - BASIC (free plan)
      // - PRO, ULTRA, MEGA, CUSTOM (paid plans)
      const subscription = getHeader('x-rapidapi-subscription') || '';

      let planType = 'UNKNOWN';

      if (subscription) {
        const subLower = subscription.toLowerCase();
        if (subLower === 'basic') {
          planType = 'FREE';
        } else if (['pro', 'ultra', 'mega', 'custom'].includes(subLower)) {
          planType = 'PAID';
        } else {
          planType = 'UNKNOWN';
        }

        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`🔍 Plan type detected from X-RapidAPI-Subscription (optional auth): "${subscription}" -> ${planType}`);
        }
      } else {
        // FALLBACK: If no subscription header, assume paid user for safety
        // This prevents accidentally limiting paid users
        planType = 'PAID';

        if (process.env.NODE_ENV !== 'production') {
          logger.debug('🔍 No X-RapidAPI-Subscription header found (optional auth), defaulting to PAID plan for safety');
        }
      }

      if (process.env.NODE_ENV !== 'production') {
        logger.debug(`Final determined plan type (optional auth): ${planType}`);
      }

      // Store RapidAPI information in request object
      // CRITICAL FIX: Always include usage object with plan type for free user detection
      // FIXED: Use actual RapidAPI key or generate a stable identifier based on user
      const effectiveKey = rapidApiKey || `proxy-auth-${rapidApiUser || 'unknown'}`;

      (req as any).rapidApi = {
        key: effectiveKey,
        user: rapidApiUser || 'unknown',
        usage: {
          limit: rateLimitLimit,
          remaining: rateLimitRemaining,
          reset: rateLimitReset,
          planType: planType
        }
      };

      // Continue to next middleware
      return next();
    }

    // We no longer use a fallback for standard RapidAPI headers without the Proxy Secret
    // This ensures that only requests with a valid Proxy Secret are authenticated as RapidAPI requests
    // All other requests must use a valid API key

    // Log the missing Proxy Secret for debugging
    if (rapidApiKey && rapidApiHost && rapidApiHost.includes('rapidapi.com')) {
      logger.warn(`Optional auth: Request with RapidAPI headers but missing valid Proxy Secret from host: ${rapidApiHost}`);
      // Do not authenticate as a RapidAPI request - fall through to API key authentication
    }

    // If not a RapidAPI request, fall back to regular optional API key authentication
    return optionalApiKeyAuth(req, _res, next);
  } catch (error) {
    next(error);
  }
};

// Re-export the regular API key auth for convenience
import { apiKeyAuth, optionalApiKeyAuth } from './apiKeyAuth';
export { apiKeyAuth, optionalApiKeyAuth };
