import { getClient } from '../config/database';
import { executeReadQuery, executeWriteQuery } from '../utils/databaseRouter';
import crypto from 'crypto';

export interface ApiKey {
  id: string;
  key: string;
  name: string;
  created_at: Date;
  expires_at: Date | null;
  is_active: boolean;
  last_used_at: Date | null;
  usage_count: number;
  subscription_type?: string;
  rate_limit_tier?: number;
}

export type SubscriptionType = 'free' | 'base' | 'premium';

export const SUBSCRIPTION_RATE_LIMITS: Record<SubscriptionType, number> = {
  free: 100,    // 100 requests per minute
  base: 500,    // 500 requests per minute
  premium: 2000 // 2000 requests per minute
};

export class ApiKeyModel {
  /**
   * Generate a new API key
   */
  static generateKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Create a new API key
   */
  static async create(
    name: string,
    expiresAt: Date | null = null,
    subscriptionType: SubscriptionType = 'free'
  ): Promise<ApiKey> {
    const key = this.generateKey();
    const rateLimitTier = SUBSCRIPTION_RATE_LIMITS[subscriptionType];

    const query = `
      INSERT INTO api_keys (key, name, expires_at, subscription_type, rate_limit_tier)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;

    const values = [key, name, expiresAt, subscriptionType, rateLimitTier];

    const result = await executeWriteQuery(query, values);
    return result.rows[0];
  }

  /**
   * Get API key by key
   */
  static async getByKey(key: string): Promise<ApiKey | null> {
    const query = 'SELECT * FROM api_keys WHERE key = $1 AND is_active = true';
    const result = await executeReadQuery(query, [key]);

    return result.rows.length ? result.rows[0] : null;
  }

  /**
   * Get API key with subscription information by key
   */
  static async getByKeyWithSubscription(key: string): Promise<ApiKey | null> {
    const query = `
      SELECT id, key, name, created_at, expires_at, is_active,
             last_used_at, usage_count, subscription_type, rate_limit_tier
      FROM api_keys
      WHERE key = $1 AND is_active = true
    `;
    const result = await executeReadQuery(query, [key]);

    return result.rows.length ? result.rows[0] : null;
  }

  /**
   * Get API key by ID
   */
  static async getById(id: string): Promise<ApiKey | null> {
    const query = 'SELECT * FROM api_keys WHERE id = $1 AND is_active = true';
    const result = await executeReadQuery(query, [id]);

    return result.rows.length ? result.rows[0] : null;
  }

  /**
   * List all API keys with pagination
   */
  static async list(
    page: number = 1,
    pageSize: number = 10
  ): Promise<{ apiKeys: ApiKey[]; total: number }> {
    const query = `
      SELECT * FROM api_keys
      WHERE is_active = true
      ORDER BY created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const countQuery = 'SELECT COUNT(*) FROM api_keys WHERE is_active = true';

    const [apiKeysResult, countResult] = await Promise.all([
      executeReadQuery(query, [pageSize, (page - 1) * pageSize]),
      executeReadQuery(countQuery),
    ]);

    return {
      apiKeys: apiKeysResult.rows,
      total: parseInt(countResult.rows[0].count),
    };
  }

  /**
   * Deactivate an API key
   */
  static async deactivate(id: string): Promise<boolean> {
    const query = 'UPDATE api_keys SET is_active = false WHERE id = $1 RETURNING id';
    const result = await executeWriteQuery(query, [id]);

    return result.rowCount !== null && result.rowCount > 0;
  }

  /**
   * Track API key usage
   */
  static async trackUsage(key: string): Promise<void> {
    const query = `
      UPDATE api_keys
      SET
        last_used_at = NOW(),
        usage_count = usage_count + 1
      WHERE key = $1
    `;

    await executeWriteQuery(query, [key]);
  }

  /**
   * Update API key subscription type and rate limit
   */
  static async updateSubscription(
    id: string,
    subscriptionType: SubscriptionType
  ): Promise<boolean> {
    const rateLimitTier = SUBSCRIPTION_RATE_LIMITS[subscriptionType];

    const query = `
      UPDATE api_keys
      SET subscription_type = $1, rate_limit_tier = $2, updated_at = NOW()
      WHERE id = $3 AND is_active = true
      RETURNING id
    `;

    const result = await executeWriteQuery(query, [subscriptionType, rateLimitTier, id]);
    return result.rowCount !== null && result.rowCount > 0;
  }

  /**
   * Clean up expired API keys
   */
  static async cleanupExpired(): Promise<number> {
    const query = `
      UPDATE api_keys
      SET is_active = false
      WHERE expires_at IS NOT NULL
      AND expires_at < NOW()
      AND is_active = true
      RETURNING id
    `;

    const result = await executeWriteQuery(query);
    return result.rowCount || 0;
  }
}
