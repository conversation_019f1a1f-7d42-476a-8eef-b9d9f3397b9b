import { Request, Response, NextFunction, RequestHandler } from 'express';
import { setCache, isRedisConnected } from '../config/redis-direct';
import logger from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

// Enhanced in-memory cache for fast access
export const memoryCache: Record<string, { data: any, expiry: number, hits?: number }> = {};

// Maximum number of items to keep in memory cache
const MAX_CACHE_ITEMS = parseInt(process.env.MAX_MEMORY_CACHE_ITEMS || '1000', 10);

// Clean up memory cache periodically
setInterval(() => {
  const now = Date.now();
  const keys = Object.keys(memoryCache);

  // If we're over the limit, remove least recently used items
  if (keys.length > MAX_CACHE_ITEMS) {
    // Sort by expiry (oldest first) and hits (lowest first)
    const sortedKeys = keys.sort((a, b) => {
      // First compare by expiry
      const expiryDiff = memoryCache[a].expiry - memoryCache[b].expiry;
      if (expiryDiff !== 0) return expiryDiff;

      // Then by hits (if available)
      const aHits = memoryCache[a].hits || 0;
      const bHits = memoryCache[b].hits || 0;
      return aHits - bHits;
    });

    // Remove oldest/least used items until we're under the limit
    const itemsToRemove = sortedKeys.length - MAX_CACHE_ITEMS;
    for (let i = 0; i < itemsToRemove; i++) {
      delete memoryCache[sortedKeys[i]];
    }
  }

  // Also remove expired items
  for (const key of keys) {
    if (memoryCache[key].expiry < now) {
      delete memoryCache[key];
    }
  }
}, 60000); // Run every minute

/**
 * Ultra-optimized cache middleware
 * - Minimal overhead for all requests
 * - Memory-only caching for high-traffic endpoints
 * - Simplified cache key generation
 * - No logging in production
 */
export const cacheMiddleware = (
  ttl: number,
  keyGenerator?: (req: Request) => string
): RequestHandler => {
  return (req: Request, res: Response, next: NextFunction) => {
    // ULTRA-FAST PATH: Skip caching for non-GET requests and health check endpoints
    if (req.method !== 'GET' || req.path === '/health' || req.path === '/healthz') {
      return next();
    }

    // Fast path detection for common endpoints
    const path = req.path;
    const isHighTrafficEndpoint =
      path === '/api/domains' ||
      path === '/api/inboxes' ||
      path.includes('/emails');

    // Add a debug flag to skip cache for debugging
    const skipCache = req.query.skipCache === 'true' || req.headers['x-skip-cache'] === 'true';
    if (skipCache) {
      if (process.env.NODE_ENV !== 'production') {
        logger.debug('Skipping cache due to skipCache parameter');
      }
      return next();
    }

    // Simplified key generation
    const key = keyGenerator ?
      keyGenerator(req) :
      `cache:${path}`;

    // Add a debug header to track the cache key
    if (process.env.NODE_ENV !== 'production') {
      res.setHeader('X-Cache-Key', key);
    }

    // Check memory cache first (fastest, no network latency)
    const now = Date.now();
    const memoryCacheEntry = memoryCache[key];

    // ULTRA-FAST PATH: Memory cache hit
    if (memoryCacheEntry && memoryCacheEntry.expiry > now) {
      // Track cache hits for LRU algorithm
      memoryCacheEntry.hits = (memoryCacheEntry.hits || 0) + 1;

      // Use direct object reference in production for maximum performance
      const responseData = {
        ...memoryCacheEntry.data,
        'request-id': (req as any).requestId || uuidv4().toUpperCase()
      };

      // Set cache headers for debugging
      res.setHeader('X-Cache', 'HIT');
      if (process.env.NODE_ENV !== 'production') {
        res.setHeader('X-Cache-TTL', Math.floor((memoryCacheEntry.expiry - now) / 1000));
      }

      // Send response directly
      res.status(200).json(responseData);
      return;
    }

    // Create a reference to the original send method
    const originalSend = res.send;

    // Ultra-optimized response caching with minimal overhead
    res.send = function(body: any) {
      // Only cache 200 OK JSON responses
      if (res.statusCode === 200 &&
          res.get('Content-Type')?.includes('application/json') &&
          req.method === 'GET') {

        try {
          // Fast JSON parsing for string bodies
          const jsonBody = typeof body === 'string' ? JSON.parse(body) : body;

          // Skip caching empty results for inboxes endpoint to fix alternating pattern
          if (path === '/api/inboxes' &&
              jsonBody.data &&
              Array.isArray(jsonBody.data.inboxes) &&
              jsonBody.data.inboxes.length === 0 &&
              jsonBody.data.pagination &&
              jsonBody.data.pagination.total === 0) {

            // Log the skipped caching for debugging
            if (process.env.NODE_ENV !== 'production') {
              logger.debug(`Skipping cache for empty inboxes result with key: ${key}`);
              res.setHeader('X-Cache', 'SKIP-EMPTY');
            }
          } else {
            // Add request ID directly
            jsonBody['request-id'] = (req as any).requestId || uuidv4().toUpperCase();

            // Store in memory cache immediately
            memoryCache[key] = {
              data: jsonBody,
              expiry: now + (ttl * 1000),
              hits: 1
            };

            // Set cache header for debugging
            if (process.env.NODE_ENV !== 'production') {
              res.setHeader('X-Cache', 'MISS');
              res.setHeader('X-Cache-Store', 'true');
            }

            // For high-traffic endpoints, skip Redis caching completely
            if (!isHighTrafficEndpoint && isRedisConnected()) {
              // Cache in Redis asynchronously without blocking
              setImmediate(() => {
                try {
                  setCache(key, jsonBody, ttl);
                } catch (e) {
                  // Ignore errors
                }
              });
            }
          }

          // If body is a string, update it with the modified JSON
          if (typeof body === 'string') {
            body = JSON.stringify(jsonBody);
          }
        } catch (e) {
          // Log errors in development
          if (process.env.NODE_ENV !== 'production') {
            logger.error(`Error in cache middleware: ${e instanceof Error ? e.message : String(e)}`);
          }
        }
      }

      // Call the original send method
      return originalSend.call(this, body);
    };

    // Continue to the next middleware
    next();
  };
};
