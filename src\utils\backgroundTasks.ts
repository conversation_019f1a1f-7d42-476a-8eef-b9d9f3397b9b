/**
 * Background Task Utility
 * 
 * This utility provides functions for running tasks in the background
 * to improve API response times by offloading non-critical operations.
 */

import logger from './logger';

/**
 * Run a task in the background using setImmediate
 * This ensures the task runs after the current event loop iteration
 * 
 * @param taskName Name of the task for logging
 * @param task Function to run in the background
 */
export const runInBackground = (taskName: string, task: () => Promise<void>): void => {
  setImmediate(async () => {
    try {
      logger.debug(`Background task started: ${taskName}`);
      const startTime = Date.now();
      
      await task();
      
      const duration = Date.now() - startTime;
      logger.debug(`Background task completed: ${taskName} (${duration}ms)`);
    } catch (error) {
      logger.error(`Background task failed: ${taskName}`, error instanceof Error ? error : new Error(String(error)));
    }
  });
};

/**
 * Run a task in the background with a delay
 * This is useful for tasks that should run after a certain period
 * 
 * @param taskName Name of the task for logging
 * @param task Function to run in the background
 * @param delayMs Delay in milliseconds before running the task
 */
export const runWithDelay = (taskName: string, task: () => Promise<void>, delayMs: number): void => {
  setTimeout(async () => {
    try {
      logger.debug(`Delayed background task started: ${taskName}`);
      const startTime = Date.now();
      
      await task();
      
      const duration = Date.now() - startTime;
      logger.debug(`Delayed background task completed: ${taskName} (${duration}ms)`);
    } catch (error) {
      logger.error(`Delayed background task failed: ${taskName}`, error instanceof Error ? error : new Error(String(error)));
    }
  }, delayMs);
};

/**
 * Queue of background tasks to be processed sequentially
 * This ensures tasks don't overwhelm the system when there are many of them
 */
interface QueuedTask {
  name: string;
  task: () => Promise<void>;
}

const taskQueue: QueuedTask[] = [];
let isProcessingQueue = false;

/**
 * Process the task queue sequentially
 */
const processTaskQueue = async (): Promise<void> => {
  if (isProcessingQueue || taskQueue.length === 0) {
    return;
  }
  
  isProcessingQueue = true;
  
  try {
    const { name, task } = taskQueue.shift()!;
    
    logger.debug(`Processing queued task: ${name} (${taskQueue.length} remaining in queue)`);
    const startTime = Date.now();
    
    await task();
    
    const duration = Date.now() - startTime;
    logger.debug(`Queued task completed: ${name} (${duration}ms)`);
  } catch (error) {
    logger.error('Error processing task queue:', error instanceof Error ? error : new Error(String(error)));
  } finally {
    isProcessingQueue = false;
    
    // Process next task if there are any
    if (taskQueue.length > 0) {
      processTaskQueue();
    }
  }
};

/**
 * Add a task to the queue to be processed sequentially
 * This is useful for tasks that should not run concurrently
 * 
 * @param taskName Name of the task for logging
 * @param task Function to run in the background
 */
export const queueTask = (taskName: string, task: () => Promise<void>): void => {
  taskQueue.push({ name: taskName, task });
  logger.debug(`Task queued: ${taskName} (queue size: ${taskQueue.length})`);
  
  // Start processing the queue if it's not already being processed
  if (!isProcessingQueue) {
    processTaskQueue();
  }
};

/**
 * Run multiple tasks in parallel with a concurrency limit
 * 
 * @param taskName Base name of the tasks for logging
 * @param tasks Array of functions to run in the background
 * @param concurrencyLimit Maximum number of tasks to run concurrently
 */
export const runWithConcurrencyLimit = async (
  taskName: string,
  tasks: Array<() => Promise<void>>,
  concurrencyLimit: number = 5
): Promise<void> => {
  logger.debug(`Starting batch of ${tasks.length} tasks with concurrency limit ${concurrencyLimit}: ${taskName}`);
  const startTime = Date.now();
  
  // Process tasks in batches
  for (let i = 0; i < tasks.length; i += concurrencyLimit) {
    const batch = tasks.slice(i, i + concurrencyLimit);
    
    try {
      await Promise.all(batch.map((task, index) => {
        return task().catch(error => {
          logger.error(`Task ${i + index} failed in batch ${taskName}:`, error instanceof Error ? error : new Error(String(error)));
        });
      }));
    } catch (error) {
      logger.error(`Error in batch ${i / concurrencyLimit} of ${taskName}:`, error instanceof Error ? error : new Error(String(error)));
    }
  }
  
  const duration = Date.now() - startTime;
  logger.debug(`Completed batch of ${tasks.length} tasks: ${taskName} (${duration}ms)`);
};
