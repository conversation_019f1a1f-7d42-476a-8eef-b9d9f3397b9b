-- CRITICAL SECURITY FIX: Expired Inbox Privacy Breach Prevention
-- This script addresses the security vulnerability where expired inboxes were being reactivated
-- instead of creating new ones, causing privacy breaches by exposing previous users' emails.

-- IMPORTANT: Run this script during a maintenance window as it will:
-- 1. Permanently delete expired inboxes and their associated emails
-- 2. Ensure complete data isolation for future inbox creations
-- 3. Prevent any possibility of accessing previous users' data

BEGIN;

-- Step 1: Log the current state before cleanup
CREATE TEMP TABLE cleanup_audit AS
SELECT 
    'BEFORE_CLEANUP' as phase,
    COUNT(*) as total_inboxes,
    COUNT(*) FILTER (WHERE is_active = true) as active_inboxes,
    COUNT(*) FILTER (WHERE is_active = false) as inactive_inboxes,
    COUNT(*) FILTER (WHERE expiry_date IS NOT NULL AND expiry_date <= NOW()) as expired_inboxes,
    COUNT(*) FILTER (WHERE is_active = false AND expiry_date IS NOT NULL AND expiry_date <= NOW()) as expired_inactive_inboxes
FROM inboxes;

-- Step 2: Identify expired inboxes that could cause privacy breaches
CREATE TEMP TABLE expired_inboxes_to_delete AS
SELECT 
    i.id,
    i.address,
    i.name,
    i.domain,
    i.expiry_date,
    i.is_active,
    i.created_at,
    i.updated_at,
    COUNT(e.id) as email_count
FROM inboxes i
LEFT JOIN emails e ON i.id = e.inbox_id
WHERE (
    -- Expired inboxes (regardless of active status)
    (i.expiry_date IS NOT NULL AND i.expiry_date <= NOW())
    OR
    -- Inactive inboxes (could be manually deactivated expired ones)
    (i.is_active = false)
)
GROUP BY i.id, i.address, i.name, i.domain, i.expiry_date, i.is_active, i.created_at, i.updated_at;

-- Step 3: Log what will be deleted for audit purposes
INSERT INTO maintenance_logs (
    operation,
    details,
    affected_rows,
    status
) 
SELECT 
    'SECURITY_CLEANUP_AUDIT',
    FORMAT('Found %s expired/inactive inboxes with %s total emails to be deleted for security',
           COUNT(*), 
           COALESCE(SUM(email_count), 0)),
    COUNT(*)::INTEGER,
    'audit'
FROM expired_inboxes_to_delete;

-- Step 4: Create a detailed audit log of what's being deleted
CREATE TEMP TABLE deletion_audit AS
SELECT 
    i.id as inbox_id,
    i.address,
    i.expiry_date,
    i.is_active,
    COUNT(e.id) as emails_to_delete,
    COUNT(a.id) as attachments_to_delete
FROM expired_inboxes_to_delete i
LEFT JOIN emails e ON i.id = e.inbox_id
LEFT JOIN attachments a ON e.id = a.email_id
GROUP BY i.id, i.address, i.expiry_date, i.is_active;

-- Step 5: Log detailed deletion audit
INSERT INTO maintenance_logs (
    operation,
    details,
    affected_rows,
    status
)
SELECT 
    'SECURITY_CLEANUP_DETAILED_AUDIT',
    FORMAT('Inbox %s (%s) - Emails: %s, Attachments: %s, Expired: %s, Active: %s',
           inbox_id, address, emails_to_delete, attachments_to_delete, 
           expiry_date, is_active),
    (emails_to_delete + attachments_to_delete)::INTEGER,
    'audit'
FROM deletion_audit;

-- Step 6: Perform the actual cleanup
-- Delete expired/inactive inboxes (this will cascade delete emails and attachments)
DELETE FROM inboxes WHERE id IN (SELECT id FROM expired_inboxes_to_delete);

-- Step 7: Get the cleanup results
INSERT INTO cleanup_audit
SELECT 
    'AFTER_CLEANUP' as phase,
    COUNT(*) as total_inboxes,
    COUNT(*) FILTER (WHERE is_active = true) as active_inboxes,
    COUNT(*) FILTER (WHERE is_active = false) as inactive_inboxes,
    COUNT(*) FILTER (WHERE expiry_date IS NOT NULL AND expiry_date <= NOW()) as expired_inboxes,
    COUNT(*) FILTER (WHERE is_active = false AND expiry_date IS NOT NULL AND expiry_date <= NOW()) as expired_inactive_inboxes
FROM inboxes;

-- Step 8: Log the final cleanup summary
INSERT INTO maintenance_logs (
    operation,
    details,
    affected_rows,
    status
)
SELECT 
    'SECURITY_CLEANUP_SUMMARY',
    FORMAT('Cleanup completed. Before: %s total (%s active, %s inactive, %s expired). After: %s total (%s active, %s inactive, %s expired)',
           b.total_inboxes, b.active_inboxes, b.inactive_inboxes, b.expired_inboxes,
           a.total_inboxes, a.active_inboxes, a.inactive_inboxes, a.expired_inboxes),
    (b.total_inboxes - a.total_inboxes)::INTEGER,
    'success'
FROM cleanup_audit b
CROSS JOIN cleanup_audit a
WHERE b.phase = 'BEFORE_CLEANUP' AND a.phase = 'AFTER_CLEANUP';

-- Step 9: Verify the fix - ensure no expired inboxes remain
DO $$
DECLARE
    remaining_expired INTEGER;
    remaining_inactive INTEGER;
BEGIN
    SELECT COUNT(*) INTO remaining_expired
    FROM inboxes 
    WHERE expiry_date IS NOT NULL AND expiry_date <= NOW();
    
    SELECT COUNT(*) INTO remaining_inactive
    FROM inboxes 
    WHERE is_active = false;
    
    IF remaining_expired > 0 OR remaining_inactive > 0 THEN
        RAISE EXCEPTION 'SECURITY FIX VERIFICATION FAILED: % expired and % inactive inboxes still remain', 
                       remaining_expired, remaining_inactive;
    END IF;
    
    -- Log successful verification
    INSERT INTO maintenance_logs (
        operation,
        details,
        affected_rows,
        status
    ) VALUES (
        'SECURITY_CLEANUP_VERIFICATION',
        'Security fix verified: No expired or inactive inboxes remain in the system',
        0,
        'success'
    );
END $$;

-- Step 10: Display cleanup summary
SELECT 
    phase,
    total_inboxes,
    active_inboxes,
    inactive_inboxes,
    expired_inboxes,
    expired_inactive_inboxes
FROM cleanup_audit
ORDER BY phase;

COMMIT;

-- Final verification query - should return no rows
SELECT 
    'VERIFICATION_CHECK' as check_type,
    COUNT(*) as count,
    'Should be 0' as expected
FROM inboxes 
WHERE (expiry_date IS NOT NULL AND expiry_date <= NOW()) 
   OR is_active = false;
