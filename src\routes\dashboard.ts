import { Router, Request, Response } from 'express';
import { executeReadQuery, executeWriteQuery } from '../utils/databaseRouter';
import { connectionPoolGuard } from '../utils/connectionPoolGuard';
import { dashboardService } from '../services/dashboardService';
import logger from '../utils/logger';

const router = Router();

// Admin authentication middleware
const adminAuth = (req: Request, res: Response, next: any): void => {
  const adminKey = req.headers['x-admin-api-key'];
  const expectedKey = process.env.ADMIN_API_KEY;

  if (!adminKey || !expectedKey || adminKey !== expectedKey) {
    res.status(401).json({ error: 'Unauthorized' });
    return;
  }

  next();
};

// Apply admin auth to all dashboard routes
router.use(adminAuth);

// Dashboard Statistics Endpoints

/**
 * GET /api/admin/dashboard/inboxes/stats
 * Returns inbox statistics including total counts and regional breakdown
 */
router.get('/inboxes/stats', async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    const stats = await dashboardService.getInboxStats();
    const duration = Date.now() - startTime;

    res.json({
      ...stats,
      responseTime: duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Dashboard inbox stats error:', error as Error);
    res.status(500).json({
      error: 'Failed to fetch inbox statistics',
      responseTime: Date.now() - startTime
    });
  }
});

/**
 * GET /api/admin/dashboard/users/stats
 * Returns user statistics including API key counts
 */
router.get('/users/stats', async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    const stats = await dashboardService.getUserStats();
    const duration = Date.now() - startTime;

    res.json({
      ...stats,
      responseTime: duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Dashboard user stats error:', error as Error);
    res.status(500).json({
      error: 'Failed to fetch user statistics',
      responseTime: Date.now() - startTime
    });
  }
});

/**
 * GET /api/admin/dashboard/requests/stats
 * Returns request statistics by region and trends
 */
router.get('/requests/stats', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // Get total request counts
    const totalResult = await executeReadQuery(`
      SELECT 
        COUNT(*) as total_requests,
        COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as last_hour,
        COUNT(CASE WHEN created_at > NOW() - INTERVAL '24 hours' THEN 1 END) as last_24h,
        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as errors
      FROM request_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `);
    
    // Get regional breakdown
    const regionalResult = await executeReadQuery(`
      SELECT 
        COALESCE(region, 'unknown') as region,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time,
        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as errors
      FROM request_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
      GROUP BY region
      ORDER BY requests DESC
    `);
    
    // Get hourly trends (last 24 hours)
    const trendsResult = await executeReadQuery(`
      SELECT 
        DATE_TRUNC('hour', created_at) as hour,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time
      FROM request_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
      GROUP BY DATE_TRUNC('hour', created_at)
      ORDER BY hour DESC
      LIMIT 24
    `);
    
    // Get endpoint popularity
    const endpointsResult = await executeReadQuery(`
      SELECT 
        endpoint,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time
      FROM request_logs 
      WHERE created_at > NOW() - INTERVAL '24 hours'
      GROUP BY endpoint
      ORDER BY requests DESC
      LIMIT 10
    `);
    
    const duration = Date.now() - startTime;
    
    res.json({
      totals: {
        requests: parseInt(totalResult.rows[0].total_requests),
        lastHour: parseInt(totalResult.rows[0].last_hour),
        last24h: parseInt(totalResult.rows[0].last_24h),
        errors: parseInt(totalResult.rows[0].errors),
        errorRate: totalResult.rows[0].total_requests > 0 
          ? (parseInt(totalResult.rows[0].errors) / parseInt(totalResult.rows[0].total_requests) * 100).toFixed(2)
          : '0.00'
      },
      regional: regionalResult.rows.map(row => ({
        region: row.region,
        requests: parseInt(row.requests),
        avgResponseTime: parseFloat(row.avg_response_time || '0').toFixed(2),
        errors: parseInt(row.errors),
        errorRate: row.requests > 0 
          ? (parseInt(row.errors) / parseInt(row.requests) * 100).toFixed(2)
          : '0.00'
      })),
      trends: trendsResult.rows.map(row => ({
        hour: row.hour,
        requests: parseInt(row.requests),
        avgResponseTime: parseFloat(row.avg_response_time || '0').toFixed(2)
      })),
      topEndpoints: endpointsResult.rows.map(row => ({
        endpoint: row.endpoint,
        requests: parseInt(row.requests),
        avgResponseTime: parseFloat(row.avg_response_time || '0').toFixed(2)
      })),
      responseTime: duration,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    logger.error('Dashboard request stats error:', error as Error);
    res.status(500).json({ 
      error: 'Failed to fetch request statistics',
      responseTime: Date.now() - startTime
    });
  }
});

/**
 * GET /api/admin/dashboard/system/health
 * Returns system health metrics including connection pools and security
 */
router.get('/system/health', async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    const health = await dashboardService.getSystemHealth();
    const duration = Date.now() - startTime;

    res.json({
      ...health,
      responseTime: duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Dashboard system health error:', error as Error);
    res.status(500).json({
      error: 'Failed to fetch system health',
      responseTime: Date.now() - startTime
    });
  }
});

/**
 * GET /api/admin/dashboard/security/stats
 * Returns security metrics including blocked requests and attack patterns
 */
router.get('/security/stats', async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    // Get security event counts from logs (last 24 hours)
    const securityResult = await executeReadQuery(`
      SELECT
        COUNT(CASE WHEN message LIKE '%Request blocked%' THEN 1 END) as blocked_requests,
        COUNT(CASE WHEN message LIKE '%Rate limit exceeded%' THEN 1 END) as rate_limited,
        COUNT(CASE WHEN message LIKE '%Security validation slow%' THEN 1 END) as slow_validations,
        COUNT(CASE WHEN message LIKE '%IP_BLOCKED%' THEN 1 END) as blocked_ips
      FROM security_logs
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `);

    // Get attack pattern breakdown
    const attackPatternsResult = await executeReadQuery(`
      SELECT
        CASE
          WHEN message LIKE '%sqlInjection%' THEN 'SQL Injection'
          WHEN message LIKE '%xss%' THEN 'XSS'
          WHEN message LIKE '%shellInjection%' THEN 'Shell Injection'
          WHEN message LIKE '%pathTraversal%' THEN 'Path Traversal'
          WHEN message LIKE '%commandInjection%' THEN 'Command Injection'
          ELSE 'Other'
        END as attack_type,
        COUNT(*) as count
      FROM security_logs
      WHERE created_at > NOW() - INTERVAL '24 hours'
      AND message LIKE '%Request blocked%'
      GROUP BY attack_type
      ORDER BY count DESC
    `);

    // Get connection pool guard statistics
    const guardStats = connectionPoolGuard.getUtilizationStats();
    const blockedIPs = connectionPoolGuard.getBlockedIPs();

    const duration = Date.now() - startTime;

    res.json({
      events: {
        blockedRequests: parseInt(securityResult.rows[0]?.blocked_requests || '0'),
        rateLimited: parseInt(securityResult.rows[0]?.rate_limited || '0'),
        slowValidations: parseInt(securityResult.rows[0]?.slow_validations || '0'),
        blockedIPs: parseInt(securityResult.rows[0]?.blocked_ips || '0')
      },
      attackPatterns: attackPatternsResult.rows.map(row => ({
        type: row.attack_type,
        count: parseInt(row.count)
      })),
      connectionGuard: {
        ...guardStats,
        currentlyBlocked: blockedIPs.length,
        blockedIPsList: blockedIPs.slice(0, 10) // Limit to first 10 for performance
      },
      responseTime: duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Dashboard security stats error:', error as Error);
    res.status(500).json({
      error: 'Failed to fetch security statistics',
      responseTime: Date.now() - startTime
    });
  }
});

/**
 * POST /api/admin/dashboard/cleanup/expired-inboxes
 * Triggers cleanup of expired inboxes
 */
router.post('/cleanup/expired-inboxes', async (req: Request, res: Response) => {
  const startTime = Date.now();

  try {
    const result = await dashboardService.cleanupExpiredInboxes();
    const duration = Date.now() - startTime;

    logger.info(`Manual cleanup completed: ${result.inboxes} inboxes, ${result.emails} emails`);

    res.json({
      success: true,
      cleaned: {
        inboxes: result.inboxes,
        emails: result.emails
      },
      beforeCount: result.beforeCount,
      responseTime: duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Dashboard cleanup error:', error as Error);
    res.status(500).json({
      error: 'Failed to cleanup expired inboxes',
      responseTime: Date.now() - startTime
    });
  }
});

/**
 * GET /api/admin/dashboard/requests/trends
 * Returns detailed request trends and patterns
 */
router.get('/requests/trends', async (req: Request, res: Response) => {
  const startTime = Date.now();
  const { period = '24h', granularity = 'hour' } = req.query;

  try {
    let interval: string;
    let truncate: string;

    switch (period) {
      case '1h':
        interval = '1 hour';
        truncate = 'minute';
        break;
      case '24h':
        interval = '24 hours';
        truncate = 'hour';
        break;
      case '7d':
        interval = '7 days';
        truncate = 'day';
        break;
      default:
        interval = '24 hours';
        truncate = 'hour';
    }

    // Get time series data
    const trendsResult = await executeReadQuery(`
      SELECT
        DATE_TRUNC('${truncate}', created_at) as time_bucket,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time,
        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as errors,
        COUNT(DISTINCT client_ip) as unique_ips
      FROM request_logs
      WHERE created_at > NOW() - INTERVAL '${interval}'
      GROUP BY DATE_TRUNC('${truncate}', created_at)
      ORDER BY time_bucket DESC
      LIMIT 100
    `);

    // Get method breakdown
    const methodsResult = await executeReadQuery(`
      SELECT
        method,
        COUNT(*) as requests,
        AVG(response_time_ms) as avg_response_time
      FROM request_logs
      WHERE created_at > NOW() - INTERVAL '${interval}'
      GROUP BY method
      ORDER BY requests DESC
    `);

    // Get status code breakdown
    const statusResult = await executeReadQuery(`
      SELECT
        status_code,
        COUNT(*) as count
      FROM request_logs
      WHERE created_at > NOW() - INTERVAL '${interval}'
      GROUP BY status_code
      ORDER BY count DESC
    `);

    const duration = Date.now() - startTime;

    res.json({
      period,
      granularity: truncate,
      trends: trendsResult.rows.map(row => ({
        time: row.time_bucket,
        requests: parseInt(row.requests),
        avgResponseTime: parseFloat(row.avg_response_time || '0').toFixed(2),
        errors: parseInt(row.errors),
        uniqueIPs: parseInt(row.unique_ips),
        errorRate: row.requests > 0
          ? (parseInt(row.errors) / parseInt(row.requests) * 100).toFixed(2)
          : '0.00'
      })),
      methods: methodsResult.rows.map(row => ({
        method: row.method,
        requests: parseInt(row.requests),
        avgResponseTime: parseFloat(row.avg_response_time || '0').toFixed(2)
      })),
      statusCodes: statusResult.rows.map(row => ({
        code: parseInt(row.status_code),
        count: parseInt(row.count)
      })),
      responseTime: duration,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Dashboard trends error:', error as Error);
    res.status(500).json({
      error: 'Failed to fetch request trends',
      responseTime: Date.now() - startTime
    });
  }
});

export default router;
