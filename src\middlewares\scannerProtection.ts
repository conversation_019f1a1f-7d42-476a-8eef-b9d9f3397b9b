import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Interface for IP tracking
interface IPTrackingEntry {
  count: number;
  lastAccessed: number;
  notFoundPaths: Set<string>;
  blocked: boolean;
  firstSeen: number;
}

// In-memory storage for tracking suspicious IPs
// This will reset when the server restarts
const suspiciousIPs: Map<string, IPTrackingEntry> = new Map();
// Separate set for quick lookup of blocked IPs
const blockedIPs: Set<string> = new Set();

// Configuration
const THRESHOLD_COUNT = 20; // Number of 404s before considering suspicious
const THRESHOLD_UNIQUE_PATHS = 8; // Number of unique 404 paths before considering suspicious
const BLOCK_DURATION = 3600000; // Block for 1 hour (in milliseconds)
const CLEANUP_INTERVAL = 3600000; // Clean up every hour (in milliseconds)
const MAX_ENTRIES = 10000; // Maximum number of IPs to track to prevent memory issues
const SAMPLING_RATE = 0.1; // Only process 10% of non-blocked requests for better performance

// Fast lookup sets and maps for better performance
const LEGITIMATE_PATHS_SET = new Set([
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/health',
  '/healthz',
  '/api/',
  '/',
  '/metrics',
  '/status'
]);

// Fast prefix check for legitimate paths
const LEGITIMATE_PATH_PREFIXES = [
  '/api/',
  '/health',
  '/status',
  '/metrics'
];

// Fast lookup for internal IPs
const INTERNAL_IP_PREFIXES = [
  '127.0.0.',
  '10.',
  '172.16.',
  '172.17.',
  '172.18.',
  '172.19.',
  '172.20.',
  '172.21.',
  '172.22.',
  '172.23.',
  '172.24.',
  '172.25.',
  '172.26.',
  '172.27.',
  '172.28.',
  '172.29.',
  '172.30.',
  '172.31.',
  '192.168.'
];

// Fast lookup for sensitive path patterns
const SENSITIVE_PATH_PATTERNS = [
  '.env',
  '.git',
  'wp-',
  'admin',
  'config',
  'phpinfo',
  'credentials',
  'aws'
];

// Optimized check for sensitive paths - only check the most common patterns
// for better performance
const isSensitivePath = (path: string): boolean => {
  for (const pattern of SENSITIVE_PATH_PATTERNS) {
    if (path.includes(pattern)) return true;
  }
  return false;
};

// Fast check for legitimate paths
const isLegitimatePath = (path: string): boolean => {
  // Direct match is fastest
  if (LEGITIMATE_PATHS_SET.has(path)) return true;

  // Check prefixes
  for (const prefix of LEGITIMATE_PATH_PREFIXES) {
    if (path.startsWith(prefix)) return true;
  }

  return false;
};

// Fast check for internal IPs
const isInternalIP = (ip: string): boolean => {
  for (const prefix of INTERNAL_IP_PREFIXES) {
    if (ip.startsWith(prefix)) return true;
  }
  return ip === 'localhost' || ip === '::1';
};

// Function to unblock IPs that might have been incorrectly blocked
const reviewBlockedIPs = () => {
  let unblockCount = 0;

  for (const [ip, data] of suspiciousIPs.entries()) {
    if (data.blocked) {
      // Check if this IP should be unblocked based on path analysis
      const allPaths = Array.from(data.notFoundPaths);

      // Count legitimate paths using our optimized function
      const legitimatePathCount = allPaths.filter(isLegitimatePath).length;

      // If more than 40% of paths are legitimate, unblock the IP
      const legitimateRatio = legitimatePathCount / allPaths.length;
      if (legitimateRatio > 0.4) {
        data.blocked = false;
        // Also remove from the blockedIPs set for fast lookups
        blockedIPs.delete(ip);
        unblockCount++;
        logger.info(`Unblocked IP ${ip} because ${Math.round(legitimateRatio * 100)}% of requests are to legitimate paths`);
      }
    }
  }

  if (unblockCount > 0) {
    logger.info(`Unblocked ${unblockCount} IPs during review`);
  }
};

// Log statistics about blocked IPs
const logBlockedIPStats = () => {
  // Use the blockedIPs set to get the count quickly
  const blockedIPCount = blockedIPs.size;

  if (blockedIPCount > 0) {
    logger.warn(`Currently blocking ${blockedIPCount} suspicious IPs`);

    // Only get detailed stats if we have blocked IPs
    const blockedIPDetails = Array.from(suspiciousIPs.entries())
      .filter(([_, data]) => data.blocked)
      .map(([ipAddress, data]) => ({
        ip: ipAddress,
        count: data.count,
        uniquePaths: data.notFoundPaths.size,
        firstSeen: new Date(data.firstSeen).toISOString(),
        lastAccessed: new Date(data.lastAccessed).toISOString()
      }));

    // Log the top 5 most active blocked IPs
    const topBlockedIPs = blockedIPDetails
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    if (topBlockedIPs.length > 0) {
      logger.warn(`Top blocked IPs: ${topBlockedIPs.map(ip =>
        `${ip.ip} (${ip.count} requests to ${ip.uniquePaths} paths)`).join(', ')}`);
    }
  }
};

// Clean up old entries to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  let count = 0;

  for (const [ip, data] of suspiciousIPs.entries()) {
    // Remove entries older than block duration if they're not blocked
    if (!data.blocked && now - data.lastAccessed > BLOCK_DURATION) {
      suspiciousIPs.delete(ip);
      count++;
    }

    // Remove blocked entries after twice the block duration
    if (data.blocked && now - data.lastAccessed > BLOCK_DURATION * 2) {
      suspiciousIPs.delete(ip);
      blockedIPs.delete(ip); // Also remove from blockedIPs set
      count++;
    }
  }

  if (count > 0) {
    logger.info(`Cleaned up ${count} IP tracking entries`);
  }

  // Only run these operations occasionally to reduce overhead
  if (Math.random() < 0.2) { // 20% chance to run these operations
    // Review blocked IPs to potentially unblock false positives
    reviewBlockedIPs();

    // Log statistics about blocked IPs
    logBlockedIPStats();
  }
}, CLEANUP_INTERVAL);

/**
 * Middleware to detect and block suspicious scanning activity
 * Optimized for performance with minimal impact on legitimate requests
 */
export const scannerProtection = (req: Request, res: Response, next: NextFunction): void => {
  // Fast path: Skip API paths and health check endpoints immediately
  if (req.path.startsWith('/api/') || req.path === '/health' || req.path === '/healthz') {
    // Still check for obvious attack patterns in API paths
    if (req.path.includes('.env') || req.path.includes('.git')) {
      // This is a suspicious request to an API path, continue with checks
    } else {
      // This is a legitimate API request, skip all checks
      return next();
    }
  }

  const ip = req.ip || 'unknown';

  // Fast path: Check if IP is already blocked using the Set for O(1) lookup
  if (blockedIPs.has(ip)) {
    // If this IP is blocked, return a 403 Forbidden
    res.status(403).json({
      'request-id': (req as any).requestId || 'BLOCKED',
      'message': 'Access denied',
      'data': null,
      'code': 403
    });
    return;
  }

  // Fast path: Check if this is an internal IP that should never be blocked
  if (isInternalIP(ip)) {
    // Internal IPs are always allowed
    return next();
  }

  const path = req.path;

  // Fast path: Check if this is a legitimate path that shouldn't count towards blocking
  if (isLegitimatePath(path)) {
    return next();
  }

  // Performance optimization: Only process a percentage of requests for non-blocked IPs
  // This significantly reduces the overhead of the scanner protection
  if (Math.random() > SAMPLING_RATE) {
    // Skip processing this request to reduce overhead
    return next();
  }

  const now = Date.now();

  // Check if IP is already being tracked
  if (suspiciousIPs.has(ip)) {
    const data = suspiciousIPs.get(ip)!;

    // Skip if already blocked (double-check)
    if (data.blocked) {
      // Add to blockedIPs set if not already there
      if (!blockedIPs.has(ip)) {
        blockedIPs.add(ip);
      }

      res.status(403).json({
        'request-id': (req as any).requestId || 'BLOCKED',
        'message': 'Access denied',
        'data': null,
        'code': 403
      });
      return;
    }

    // Update tracking data
    data.count++;
    data.lastAccessed = now;
    data.notFoundPaths.add(path);

    // Only check for blocking if we've reached certain thresholds
    // to avoid unnecessary processing
    if (data.count >= THRESHOLD_COUNT / 2) {
      // Check if this IP should be blocked
      const isSuspicious =
        data.count >= THRESHOLD_COUNT &&
        data.notFoundPaths.size >= THRESHOLD_UNIQUE_PATHS;

      // Additional check for sensitive paths - require at least 3 sensitive paths
      // to avoid false positives
      const sensitivePaths = Array.from(data.notFoundPaths).filter(isSensitivePath);
      const hasSensitivePaths = sensitivePaths.length >= 3;

      if (isSuspicious || hasSensitivePaths) {
        // Add a safety check - don't block if more than 50% of requests are to legitimate paths
        const allPaths = Array.from(data.notFoundPaths);
        const legitimatePathCount = allPaths.filter(isLegitimatePath).length;
        const legitimateRatio = legitimatePathCount / allPaths.length;

        if (legitimateRatio > 0.5) {
          // This is likely a legitimate user, don't block
          if (Math.random() < 0.1) { // Only log occasionally to reduce noise
            logger.info(`Not blocking IP ${ip} despite suspicious activity because ${Math.round(legitimateRatio * 100)}% of requests are to legitimate paths`);
          }
          return next();
        }

        // Block this IP
        data.blocked = true;
        blockedIPs.add(ip); // Add to fast lookup set

        // Log the blocking event with more details
        logger.warn(`Blocked suspicious IP ${ip} after ${data.count} requests to ${data.notFoundPaths.size} unique paths (${sensitivePaths.length} sensitive paths)`);

        // Return 403 Forbidden
        res.status(403).json({
          'request-id': (req as any).requestId || 'BLOCKED',
          'message': 'Access denied',
          'data': null,
          'code': 403
        });
        return;
      }
    }
  } else {
    // If we've reached the maximum number of entries, don't add more
    if (suspiciousIPs.size >= MAX_ENTRIES) {
      // Just let the request through without tracking
      return next();
    }

    // Start tracking this IP
    suspiciousIPs.set(ip, {
      count: 1,
      lastAccessed: now,
      notFoundPaths: new Set([path]),
      blocked: false,
      firstSeen: now
    });
  }

  // Continue to next middleware
  next();
};

export default scannerProtection;
