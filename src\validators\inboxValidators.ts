import Joi from 'joi';

// Schema for creating a new inbox
export const createInboxSchema = Joi.object({
  name: Joi.string()
    .pattern(/^[a-zA-Z0-9_-]+$/)
    .allow('')
    .optional()
    .messages({
      'string.base': 'Name must be a string',
      'string.pattern.base': 'Name must contain only letters, numbers, underscores, and hyphens',
    }),

  domain: Joi.string()
    .pattern(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
    .messages({
      'string.base': 'Domain must be a string',
      'string.pattern.base': 'Domain must be a valid domain name',
    }),

  lifespan: Joi.number()
    .integer()
    .min(0)
    .messages({
      'number.base': 'Lifespan must be a number',
      'number.integer': 'Lifespan must be an integer',
      'number.min': 'Lifespan must be at least 0',
    }),
});

// Schema for listing inboxes
export const listInboxesSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),

  size: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Size must be a number',
      'number.integer': 'Size must be an integer',
      'number.min': 'Size must be at least 1',
      'number.max': 'Size cannot be greater than 100',
    }),

  name: Joi.string()
    .optional()
    .allow('')
    .messages({
      'string.base': 'Name filter must be a string',
    }),
});

// Schema for inbox ID parameter
export const inboxIdSchema = Joi.object({
  id: Joi.string()
    .uuid()
    .optional()
    .messages({
      'string.base': 'Inbox ID must be a string',
      'string.empty': 'Inbox ID is required',
      'string.uuid': 'Inbox ID must be a valid UUID',
    }),
  inboxId: Joi.string()
    .uuid()
    .optional()
    .messages({
      'string.base': 'Inbox ID must be a string',
      'string.empty': 'Inbox ID is required',
      'string.uuid': 'Inbox ID must be a valid UUID',
    }),
})
.or('id', 'inboxId')
.messages({
  'object.missing': 'Inbox ID is required',
});
