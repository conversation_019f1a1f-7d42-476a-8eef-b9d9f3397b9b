import Joi from 'joi';

export const emailValidators = {
  // Validator for listing emails
  listEmails: Joi.object({
    page: Joi.number().integer().min(1).optional(),
    size: Joi.number().integer().min(1).max(100).optional(),
    from: Joi.string().optional(),
    subject: Joi.string().optional(),
    hasAttachments: Joi.boolean().optional(),
  }),

  // Validator for getting a specific email
  getEmail: Joi.object({
    inboxId: Joi.string().uuid().required(),
    id: Joi.string().uuid().optional(),  // For compatibility
    emailId: Joi.string().uuid().required(),
  }),

  // Validator for getting an attachment
  getAttachment: Joi.object({
    inboxId: Joi.string().uuid().required(),
    id: Joi.string().uuid().optional(),  // For compatibility
    emailId: Joi.string().uuid().required(),
    attachmentId: Joi.string().uuid().required(),
  }),

  // Validator for deleting an email
  deleteEmail: Joi.object({
    inboxId: Joi.string().uuid().required(),
    id: Joi.string().uuid().optional(),  // For compatibility
    emailId: Joi.string().uuid().required(),
  }),
};
