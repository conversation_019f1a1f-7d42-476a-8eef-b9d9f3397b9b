// PHASE 2: Combined security headers middleware for better performance
// Combines helmet() and additionalSecurityHeaders into a single middleware
// to reduce middleware chain overhead

import { Request, Response, NextFunction } from 'express';

/**
 * Combined security headers middleware
 * Replaces both helmet() and additionalSecurityHeaders for better performance
 */
export const combinedSecurityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // PHASE 2: Fast path for health checks - skip all security headers
  if (req.path === '/health' || req.path === '/healthz') {
    return next();
  }

  // Set all security headers in one go to reduce overhead
  res.set({
    // Helmet equivalent headers
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'",
    
    // Additional security headers (from additionalSecurityHeaders)
    'X-Powered-By': 'TempFly.io',
    'X-API-Version': '1.0',
    'X-Rate-Limit-Policy': 'standard',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
    
    // Performance headers
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  });

  next();
};

/**
 * Lightweight security headers for high-traffic endpoints
 * Minimal security headers for domains, inboxes endpoints
 */
export const lightweightSecurityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Only set essential security headers for high-traffic endpoints
  res.set({
    'X-Content-Type-Options': 'nosniff',
    'X-Powered-By': 'TempFly.io',
    'Cache-Control': 'no-cache, no-store, must-revalidate'
  });

  next();
};

export default combinedSecurityHeaders;
