/**
 * Validates an email address format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

/**
 * Validates an inbox name format
 */
export const isValidInboxName = (name: string): boolean => {
  // Only letters, numbers, underscores, and hyphens
  const nameRegex = /^[a-zA-Z0-9_-]+$/;

  // Check if the name matches the regex
  if (!nameRegex.test(name)) {
    return false;
  }

  // Check for restricted usernames
  const restrictedUsernames = [
    'admin', 'administrator', 'support', 'help', 'contact', 'info', 'no-reply', 'noreply',
    'postmaster', 'webmaster', 'hostmaster', 'abuse', 'security', 'billing', 'sales', 'service',
    'mail', 'email', 'system', 'root', 'staff', 'team', 'official', 'account', 'feedback'
  ];

  // Return false if the name is in the restricted list (case-insensitive)
  return !restrictedUsernames.some(restricted => name.toLowerCase() === restricted.toLowerCase());
};

/**
 * Validates a domain format
 */
export const isValidDomain = (domain: string): boolean => {
  // Basic domain validation, no @ symbol
  const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
};
