import { Pool, PoolClient, QueryResult } from 'pg';
import { writePool, readPool, getWritePoolHealth, getReadPoolHealth, getDatabaseConfig } from '../config/database-pools';
import logger from './logger';
import { databaseCircuitBreaker } from './circuitBreaker';
import { TIMEOUTS, withTimeout } from '../config/timeouts';
import { connectionPoolGuard, getCurrentRequestIP } from './connectionPoolGuard';

// Query operation types
export enum QueryType {
  READ = 'READ',
  WRITE = 'WRITE',
  TRANSACTION = 'TRANSACTION'
}

// Regional retry configuration with unified timeouts
const instanceId = process.env.INSTANCE_ID || 'unknown';
const region = process.env.REGION || 'unknown';
const isHighLatencyRegion = region.toLowerCase().includes('india') ||
                            region.toLowerCase().includes('in') ||
                            instanceId.toLowerCase().includes('india') ||
                            instanceId.toLowerCase().includes('in');

const RETRY_CONFIG = {
  maxRetries: isHighLatencyRegion ? 5 : 3,
  retryDelays: isHighLatencyRegion
    ? [2000, 4000, 8000, 16000, 30000] // Longer delays for high-latency regions
    : [1000, 2000, 4000], // Standard delays
  connectionTimeout: isHighLatencyRegion ? 30000 : 15000, // 30s vs 15s
  queryTimeout: isHighLatencyRegion ? 60000 : 30000, // 60s vs 30s
};

// Determine query type based on SQL statement
export function determineQueryType(sql: string): QueryType {
  const normalizedSql = sql.trim().toUpperCase();
  
  // Transaction control statements
  if (normalizedSql.startsWith('BEGIN') || 
      normalizedSql.startsWith('COMMIT') || 
      normalizedSql.startsWith('ROLLBACK')) {
    return QueryType.TRANSACTION;
  }
  
  // Write operations
  if (normalizedSql.startsWith('INSERT') ||
      normalizedSql.startsWith('UPDATE') ||
      normalizedSql.startsWith('DELETE') ||
      normalizedSql.startsWith('CREATE') ||
      normalizedSql.startsWith('DROP') ||
      normalizedSql.startsWith('ALTER') ||
      normalizedSql.startsWith('TRUNCATE')) {
    return QueryType.WRITE;
  }
  
  // Default to read for SELECT and other operations
  return QueryType.READ;
}

// Execute query with retry logic
async function executeWithRetry<T>(
  operation: () => Promise<T>,
  operationType: string,
  targetDatabase: string
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt <= RETRY_CONFIG.maxRetries; attempt++) {
    try {
      const result = await operation();
      
      if (attempt > 0) {
        logger.info(`${operationType} succeeded on attempt ${attempt + 1} (${targetDatabase})`);
      }
      
      return result;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt < RETRY_CONFIG.maxRetries) {
        const delay = RETRY_CONFIG.retryDelays[attempt];
        logger.warn(`${operationType} failed on attempt ${attempt + 1}, retrying in ${delay}ms (${targetDatabase}): ${lastError.message}`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        logger.error(`${operationType} failed after ${RETRY_CONFIG.maxRetries + 1} attempts (${targetDatabase}): ${lastError.message}`);
      }
    }
  }
  
  throw lastError;
}

// Get appropriate pool based on query type and health status
function getPoolForQuery(queryType: QueryType): { pool: Pool; poolName: string; shouldFallback: boolean } {
  const writeHealth = getWritePoolHealth();
  const readHealth = getReadPoolHealth();
  
  switch (queryType) {
    case QueryType.WRITE:
    case QueryType.TRANSACTION:
      // Always use write pool for writes and transactions
      return {
        pool: writePool,
        poolName: 'write',
        shouldFallback: false // No fallback for writes - read replica is read-only
      };
      
    case QueryType.READ:
      // Use read pool if healthy, otherwise fallback to write pool
      if (readPool !== writePool && readHealth.isHealthy) {
        return {
          pool: readPool,
          poolName: 'read',
          shouldFallback: true
        };
      } else {
        const usingFallback = readPool !== writePool && !readHealth.isHealthy;
        if (usingFallback) {
          logger.debug('Read pool unhealthy, falling back to write pool');
        }
        return {
          pool: writePool,
          poolName: usingFallback ? 'write (fallback)' : 'write',
          shouldFallback: false
        };
      }
      
    default:
      return {
        pool: writePool,
        poolName: 'write',
        shouldFallback: false
      };
  }
}

// Main query execution function
export async function executeQuery(
  sql: string,
  values?: any[],
  forcePool?: 'read' | 'write'
): Promise<QueryResult> {
  const queryType = forcePool ? 
    (forcePool === 'write' ? QueryType.WRITE : QueryType.READ) : 
    determineQueryType(sql);
  
  const { pool, poolName, shouldFallback } = forcePool ? 
    { 
      pool: forcePool === 'write' ? writePool : readPool, 
      poolName: forcePool, 
      shouldFallback: forcePool === 'read' && readPool !== writePool 
    } : 
    getPoolForQuery(queryType);
  
  const operationType = `${queryType} query`;
  const startTime = Date.now();
  
  try {
    // Use circuit breaker for database operations
    const result = await databaseCircuitBreaker.execute(
      async () => {
        // Add timeout protection to the query
        return await withTimeout(
          executeWithRetry(
            () => pool.query(sql, values),
            operationType,
            poolName
          ),
          TIMEOUTS.DB_QUERY,
          `Database query timeout after ${TIMEOUTS.DB_QUERY}ms`
        );
      },
      async () => {
        // Fallback when circuit is open
        throw new Error(`Database circuit breaker is open - service temporarily unavailable`);
      }
    );

    const duration = Date.now() - startTime;
    logger.debug(`${operationType} executed successfully (${poolName}, ${duration}ms)`);

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    const err = error instanceof Error ? error : new Error(String(error));
    
    // Try fallback for read queries if available and not already using fallback
    if (queryType === QueryType.READ && shouldFallback && poolName === 'read') {
      logger.warn(`Read query failed on read pool, attempting fallback to write pool: ${err.message}`);
      
      try {
        const fallbackResult = await executeWithRetry(
          () => writePool.query(sql, values),
          `${operationType} (fallback)`,
          'write (fallback)'
        );
        
        const totalDuration = Date.now() - startTime;
        logger.info(`${operationType} succeeded on fallback (write, ${totalDuration}ms)`);
        
        return fallbackResult;
      } catch (fallbackError) {
        const fallbackErr = fallbackError instanceof Error ? fallbackError : new Error(String(fallbackError));
        logger.error(`${operationType} failed on both read and write pools: ${fallbackErr.message}`);
        throw fallbackErr;
      }
    }
    
    logger.error(`${operationType} failed (${poolName}, ${duration}ms): ${err.message}`);
    throw err;
  }
}

// Get client for transactions with circuit breaker and connection pool protection
export async function getClient(forcePool?: 'read' | 'write'): Promise<PoolClient> {
  const pool = forcePool === 'read' ? readPool : writePool;
  const poolName = forcePool === 'read' ? 'read' : 'write';
  const clientIP = getCurrentRequestIP();

  // Check connection pool guard before attempting connection
  const connectionAllowed = await connectionPoolGuard.guardConnection(clientIP, poolName);
  if (!connectionAllowed) {
    throw new Error(`Connection limit exceeded for IP ${clientIP} or pool ${poolName}`);
  }

  try {
    // Use circuit breaker for client acquisition
    const client = await databaseCircuitBreaker.execute(
      async () => {
        return await withTimeout(
          executeWithRetry(
            () => pool.connect(),
            'Get client',
            poolName
          ),
          TIMEOUTS.DB_CONNECTION,
          `Database connection timeout after ${TIMEOUTS.DB_CONNECTION}ms`
        );
      },
      async () => {
        // Release connection guard on circuit breaker failure
        connectionPoolGuard.releaseConnection(clientIP, poolName);
        throw new Error(`Database circuit breaker is open - cannot acquire client`);
      }
    );

    // Wrap the client release method to update connection guard
    const originalRelease = client.release.bind(client);
    client.release = (err?: Error | boolean) => {
      connectionPoolGuard.releaseConnection(clientIP, poolName);
      return originalRelease(err);
    };

    logger.debug(`Client acquired from ${poolName} pool for IP ${clientIP}`);
    return client;
  } catch (error) {
    // Release connection guard on error
    connectionPoolGuard.releaseConnection(clientIP, poolName);
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`Failed to get client from ${poolName} pool: ${err.message}`);
    throw err;
  }
}

// Convenience functions for explicit pool selection
export const executeReadQuery = (sql: string, values?: any[]) => 
  executeQuery(sql, values, 'read');

export const executeWriteQuery = (sql: string, values?: any[]) => 
  executeQuery(sql, values, 'write');

// Re-export getDatabaseConfig for convenience
export { getDatabaseConfig } from '../config/database-pools';

// Health check functions
export async function checkDatabaseHealth(): Promise<{
  write: { healthy: boolean; latency?: number; error?: string };
  read: { healthy: boolean; latency?: number; error?: string };
}> {
  const results = {
    write: { healthy: false, latency: undefined as number | undefined, error: undefined as string | undefined },
    read: { healthy: false, latency: undefined as number | undefined, error: undefined as string | undefined }
  };
  
  // Check write pool
  try {
    const startTime = Date.now();
    await writePool.query('SELECT 1 AS health_check');
    results.write.healthy = true;
    results.write.latency = Date.now() - startTime;
  } catch (error) {
    results.write.error = error instanceof Error ? error.message : String(error);
  }
  
  // Check read pool (if different from write pool)
  if (readPool !== writePool) {
    try {
      const startTime = Date.now();
      await readPool.query('SELECT 1 AS health_check');
      results.read.healthy = true;
      results.read.latency = Date.now() - startTime;
    } catch (error) {
      results.read.error = error instanceof Error ? error.message : String(error);
    }
  } else {
    // Same as write pool
    results.read = { ...results.write };
  }
  
  return results;
}
