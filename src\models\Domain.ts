import { executeReadQuery, executeWriteQuery } from '../utils/databaseRouter';

export interface Domain {
  id: string;
  domain: string;
  is_active: boolean;
  created_at: Date;
}

export class DomainModel {
  /**
   * Get all active domains
   */
  static async getAll(): Promise<Domain[]> {
    const query = 'SELECT * FROM domains WHERE is_active = true ORDER BY domain';
    const result = await executeReadQuery(query);
    return result.rows;
  }

  /**
   * Add a new domain
   */
  static async create(domain: string): Promise<Domain> {
    const query = 'INSERT INTO domains (domain) VALUES ($1) RETURNING *';
    const result = await executeWriteQuery(query, [domain]);
    return result.rows[0];
  }

  /**
   * Deactivate a domain
   */
  static async deactivate(id: string): Promise<boolean> {
    const query = 'UPDATE domains SET is_active = false WHERE id = $1 RETURNING id';
    const result = await executeWriteQuery(query, [id]);
    return result.rowCount !== null && result.rowCount > 0;
  }

  /**
   * Get a random active domain
   */
  static async getRandomDomain(): Promise<string | null> {
    const query = 'SELECT domain FROM domains WHERE is_active = true ORDER BY RANDOM() LIMIT 1';
    const result = await executeReadQuery(query);

    return result.rows.length ? result.rows[0].domain : null;
  }

  /**
   * Check if a domain is valid (exists and is active)
   */
  static async isValidDomain(domain: string): Promise<boolean> {
    const query = 'SELECT id FROM domains WHERE domain = $1 AND is_active = true';
    const result = await executeReadQuery(query, [domain]);
    return result.rows.length > 0;
  }
}
