# Redis configuration file for TempFly.io API
# Optimized for OVHcloud dedicated server with 32GB RAM

# NETWORK
bind 0.0.0.0
protected-mode yes
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300

# GENERAL
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# MEMORY MANAGEMENT - PHASE 1: Optimized for reduced memory usage
maxmemory 1024mb
maxmemory-policy allkeys-lru
maxmemory-samples 5
replica-ignore-maxmemory yes

# LAZY FREEING
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# APPEND ONLY MODE
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite yes
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# RDB PERSISTENCE
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# CLIENTS
maxclients 10000

# MEMORY MANAGEMENT
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes

# LATENCY MONITORING
latency-monitor-threshold 0

# DEFRAGMENTATION
activedefrag yes
active-defrag-ignore-bytes 100mb
active-defrag-threshold-lower 10
active-defrag-threshold-upper 100
active-defrag-cycle-min 5
active-defrag-cycle-max 75
active-defrag-max-scan-fields 1000

# TLS/SSL
tls-port 0
tls-cert-file ""
tls-key-file ""
tls-auth-clients no
tls-replication no
tls-cluster no
tls-protocols "TLSv1.2 TLSv1.3"
tls-ciphers ""
tls-ciphersuites ""
tls-prefer-server-ciphers no
tls-session-caching yes
tls-session-cache-size 20480
tls-session-cache-timeout 300
