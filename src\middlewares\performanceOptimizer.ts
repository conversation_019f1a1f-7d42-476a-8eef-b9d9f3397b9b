import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Performance optimization middleware for inbox creation
 * This middleware optimizes the request processing pipeline for better performance
 */
export const optimizeInboxCreation = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const startTime = Date.now();

  // Set aggressive timeout for inbox creation
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      logger.error('Inbox creation timeout after 8 seconds');
      res.status(408).json({
        error: 'Request timeout',
        message: 'Inbox creation took too long. Please try again.',
        code: 'TIMEOUT_ERROR'
      });
    }
  }, 8000); // 8 second timeout

  // Clear timeout when response is sent
  res.on('finish', () => {
    clearTimeout(timeout);
    const duration = Date.now() - startTime;

    // Log performance metrics
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Inbox creation completed in ${duration}ms`);
    }

    // Log slow requests
    if (duration > 3000) {
      logger.warn(`Slow inbox creation detected: ${duration}ms for ${req.method} ${req.path}`);
    }
  });

  // Optimize request processing
  req.startTime = startTime;

  next();
};

/**
 * Fast path middleware for non-free users
 * Skips expensive operations for users who don't need limit enforcement
 * FIXED: Now properly checks plan type instead of relying on unreliable headers
 */
export const fastPathForPaidUsers = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // CRITICAL FIX: Check the actual plan type from RapidAPI authentication
    // instead of relying on free user headers which may not be present
    const rapidApiData = (req as any).rapidApi;

    if (rapidApiData && rapidApiData.usage && rapidApiData.usage.planType) {
      const planType = rapidApiData.usage.planType.toUpperCase();

      // Only enable fast path for confirmed PAID users
      // Never enable fast path for FREE users or UNKNOWN plan types
      if (planType === 'PAID') {
        (req as any).fastPath = true;
        if (process.env.NODE_ENV !== 'production') {
          logger.debug('Fast path enabled for confirmed paid user (planType: PAID)');
        }
      } else if (planType === 'FREE') {
        // Explicitly disable fast path for free users
        (req as any).fastPath = false;
        if (process.env.NODE_ENV !== 'production') {
          logger.debug('Fast path DISABLED for free user (planType: FREE)');
        }
      } else {
        // For UNKNOWN plan types, be conservative and disable fast path
        (req as any).fastPath = false;
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Fast path DISABLED for unknown plan type: ${planType}`);
        }
      }
    } else {
      // If no RapidAPI data, this might be a direct API key request
      // Don't set fast path - let other middleware handle it
      if (process.env.NODE_ENV !== 'production') {
        logger.debug('No RapidAPI data found, not setting fast path');
      }
    }

    next();
  } catch (error) {
    // Don't let optimization errors break the request
    logger.debug('Fast path optimization failed, continuing normally:', error instanceof Error ? error.message : String(error));
    next();
  }
};

/**
 * Request optimization middleware
 * Sets up various optimizations for the request pipeline
 */
export const optimizeRequest = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Set response headers for better performance
  res.set({
    'X-Powered-By': 'TempFly.io',
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  });

  // Enable compression for JSON responses
  if (req.headers.accept && req.headers.accept.includes('application/json')) {
    res.set('Content-Type', 'application/json; charset=utf-8');
  }

  next();
};

/**
 * Database connection optimization
 * Ensures database connections are ready and optimized
 */
export const optimizeDatabase = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Pre-warm database connection if needed
    // This is a no-op for now but can be extended
    next();
  } catch (error) {
    logger.error('Database optimization failed:', error instanceof Error ? error : new Error(String(error)));
    next(error instanceof Error ? error : new Error(String(error)));
  }
};

/**
 * Combined performance middleware
 * Applies all performance optimizations in the correct order
 */
export const performanceMiddleware = [
  optimizeRequest,
  fastPathForPaidUsers,
  optimizeInboxCreation
];

// Extend Request interface to include performance tracking
declare global {
  namespace Express {
    interface Request {
      startTime?: number;
      fastPath?: boolean;
    }
  }
}
