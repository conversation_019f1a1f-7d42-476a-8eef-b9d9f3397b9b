import { getClient } from '../config/database';
import { AppError } from '../middlewares/errorHandler';
import logger from '../utils/logger';
import { executeReadQuery, executeWriteQuery } from '../utils/databaseRouter';

export interface EmailAttachment {
  id?: string;
  email_id: string;
  filename: string;
  content_type: string;
  size: number;
  content_id?: string;
  is_inline: boolean;
  created_at?: Date;
}

export interface EmailAttachmentWithContent extends EmailAttachment {
  content: Buffer;
}

export interface Email {
  id?: string;
  inbox_id: string;
  from_address: string;
  from_name?: string;
  to_address: string;
  subject?: string;
  text_content?: string;
  html_content?: string;
  headers?: Record<string, any>;
  has_attachments: boolean;
  received_at?: Date;
  read_at?: Date;
  is_deleted: boolean;
  created_at?: Date;
  updated_at?: Date;
  spam_score?: number;
  attachments?: EmailAttachment[];
}

export class EmailModel {
  /**
   * Generate a new UUID for an email
   * This allows us to return an ID immediately before saving the email
   */
  static async generateId(): Promise<string> {
    try {
      const result = await executeReadQuery('SELECT gen_random_uuid() AS id');
      return result.rows[0].id;
    } catch (error) {
      logger.error('Failed to generate email ID', error as Error);
      throw new AppError('Failed to generate email ID', 500);
    }
  }

  /**
   * Get emails for an inbox with timeout protection
   */
  static async getByInboxId(
    inboxId: string,
    page: number = 1,
    size: number = 10,
    filters: Record<string, any> = {},
    apiKeyId?: string // Optional API key ID for ownership verification
  ): Promise<{ emails: Email[]; total: number; page: number; size: number; pages: number }> {
    const startTime = Date.now();

    try {
      // Use a single optimized query with window functions to get both data and count
      // This eliminates the need for a separate count query
      let query = `
        WITH filtered_emails AS (
          SELECT
            id, inbox_id, from_address, from_name, to_address, subject,
            received_at, read_at, has_attachments,
            COUNT(*) OVER() AS total_count
          FROM emails
          WHERE inbox_id = $1
      `;

      // Only log in development mode
      if (process.env.NODE_ENV !== 'production') {
        process.stdout.write(`Executing optimized query for inbox ${inboxId}\n`);
      }

      const values: any[] = [inboxId];
      let paramIndex = 2;

      // PHASE 4: Apply filters using optimized search methods
      if (filters.from) {
        // PHASE 4: Use full-text search for better performance when available
        // Fall back to ILIKE for compatibility
        if (filters.from.length > 2) {
          // Use full-text search for longer queries (more efficient)
          query += ` AND to_tsvector('english', from_address) @@ plainto_tsquery('english', $${paramIndex})`;
          values.push(filters.from);
        } else {
          // Use ILIKE for short queries (full-text search not efficient for short terms)
          query += ` AND from_address ILIKE $${paramIndex}`;
          values.push(`%${filters.from}%`);
        }
        paramIndex++;
      }

      if (filters.subject) {
        query += ` AND subject ILIKE $${paramIndex}`;
        values.push(`%${filters.subject}%`);
        paramIndex++;
      }

      if (filters.hasAttachments !== undefined) {
        query += ` AND has_attachments = $${paramIndex}`;
        values.push(filters.hasAttachments);
        paramIndex++;
      }

      // Close the CTE and add ordering and pagination
      // Use parameterized queries for all values to prevent SQL injection
      query += `
        )
        SELECT * FROM filtered_emails
        ORDER BY received_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      values.push(size, (page - 1) * size);

      // Add timeout protection to the database query using read pool
      const queryPromise = executeReadQuery(query, values);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Email query timeout after 5 seconds for inbox ${inboxId}`));
        }, 5000); // 5 second timeout for email queries
      });

      // Race the query against the timeout
      const result = await Promise.race([queryPromise, timeoutPromise]);
      const emails = result.rows;

      // Get total count from the first row (if available)
      const total = emails.length > 0 ? parseInt(emails[0].total_count) : 0;

      // Calculate pagination
      const pages = Math.ceil(total / size);

      const executionTime = Date.now() - startTime;

      // Performance monitoring
      if (executionTime > 2000) {
        logger.warn(`Slow email query detected: ${executionTime}ms for inbox ${inboxId}`);
      }

      // Only log in development mode
      if (process.env.NODE_ENV !== 'production' && emails.length > 0) {
        process.stdout.write(`Found ${emails.length} of ${total} total emails for inbox ${inboxId} in ${executionTime}ms\n`);
      }

      return {
        emails,
        total,
        page,
        size,
        pages,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error(`Failed to get emails for inbox ${inboxId} (${executionTime}ms)`, error as Error);
      process.stdout.write(`Error getting emails: ${(error as Error).message}\n`);

      // Return empty results instead of throwing an error to prevent cascading failures
      return {
        emails: [],
        total: 0,
        page,
        size,
        pages: 0,
      };
    }
  }

  /**
   * Get an email by ID - PHASE 1: Optimized to fix N+1 query pattern
   */
  static async getById(id: string, inboxId: string): Promise<Email> {
    try {
      // PHASE 1: Use LEFT JOIN to fetch email and attachments in a single query
      // This eliminates the N+1 query pattern and reduces database round trips
      const query = `
        SELECT
          e.*,
          COALESCE(
            json_agg(
              json_build_object(
                'id', a.id,
                'filename', a.filename,
                'content_type', a.content_type,
                'size', a.size,
                'content_id', a.content_id,
                'is_inline', a.is_inline,
                'created_at', a.created_at
              )
            ) FILTER (WHERE a.id IS NOT NULL),
            '[]'::json
          ) as attachments_json
        FROM emails e
        LEFT JOIN attachments a ON e.id = a.email_id
        WHERE e.id = $1 AND e.inbox_id = $2
        GROUP BY e.id
      `;

      const result = await executeReadQuery(query, [id, inboxId]);

      if (result.rows.length === 0) {
        throw new AppError('Email not found', 404);
      }

      const email = result.rows[0];

      // Parse attachments from JSON aggregation
      if (email.attachments_json && Array.isArray(email.attachments_json) && email.attachments_json.length > 0) {
        email.attachments = email.attachments_json;
      } else {
        email.attachments = [];
      }

      // Remove the temporary JSON field
      delete email.attachments_json;

      return email;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error(`Failed to get email ${id}`, error as Error);
      throw new AppError('Failed to get email', 500);
    }
  }

  /**
   * Get an attachment by ID
   */
  static async getAttachment(attachmentId: string, emailId: string): Promise<EmailAttachmentWithContent> {
    try {
      const query = 'SELECT * FROM attachments WHERE id = $1 AND email_id = $2';
      const result = await executeReadQuery(query, [attachmentId, emailId]);

      if (result.rows.length === 0) {
        throw new AppError('Attachment not found', 404);
      }

      // For this implementation, we're not actually storing attachment content
      // So we'll create a mock content for demonstration purposes
      const attachment = result.rows[0];

      // Create a mock content buffer with the attachment information
      // In a real implementation, this would be retrieved from storage
      const mockContent = Buffer.from(`This is a mock attachment content for ${attachment.filename}. In a real implementation, this would be the actual file content.`);

      return {
        ...attachment,
        content: mockContent
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error(`Failed to get attachment ${attachmentId}`, error as Error);
      throw new AppError('Failed to get attachment', 500);
    }
  }

  /**
   * Mark an email as read
   */
  static async markAsRead(id: string, inboxId: string): Promise<Date> {
    try {
      const query = 'UPDATE emails SET read_at = NOW(), updated_at = NOW() WHERE id = $1 AND inbox_id = $2 RETURNING read_at';
      const result = await executeWriteQuery(query, [id, inboxId]);

      if (result.rowCount === 0) {
        throw new AppError('Email not found', 404);
      }

      // Return the new read_at timestamp
      return result.rows[0].read_at;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error(`Failed to mark email ${id} as read`, error as Error);
      throw new AppError('Failed to mark email as read', 500);
    }
  }

  /**
   * Delete an email (hard delete)
   */
  static async delete(id: string, inboxId: string): Promise<void> {
    let client;
    try {
      // Get a client from the write pool for transaction
      client = await getClient('write');

      // Start transaction
      await client.query('BEGIN');

      // First, delete any attachments associated with the email
      const deleteAttachmentsQuery = 'DELETE FROM attachments WHERE email_id = $1';
      await client.query(deleteAttachmentsQuery, [id]);

      // Then, delete the email itself
      const deleteEmailQuery = 'DELETE FROM emails WHERE id = $1 AND inbox_id = $2';
      const result = await client.query(deleteEmailQuery, [id, inboxId]);

      // Check if the email was found and deleted
      if (result.rowCount === 0) {
        await client.query('ROLLBACK');
        throw new AppError('Email not found', 404);
      }

      // Commit the transaction
      await client.query('COMMIT');

      logger.info(`Email ${id} permanently deleted from inbox ${inboxId}`);
    } catch (error) {
      // Rollback the transaction if there was an error
      if (client) {
        await client.query('ROLLBACK');
      }

      if (error instanceof AppError) {
        throw error;
      }

      logger.error(`Failed to delete email ${id}`, error as Error);
      throw new AppError('Failed to delete email', 500);
    } finally {
      // Release the client back to the pool
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Create a new email
   */
  static async create(email: Email): Promise<Email> {
    let client;
    try {
      client = await getClient('write');
      await client.query('BEGIN');

      // Insert the email
      const emailQuery = `
        INSERT INTO emails (
          ${email.id ? 'id, ' : ''}inbox_id, from_address, from_name, to_address, subject,
          text_content, html_content, headers, has_attachments, is_deleted, spam_score
        ) VALUES (
          ${email.id ? '$1, ' : ''}$${email.id ? '2' : '1'}, $${email.id ? '3' : '2'}, $${email.id ? '4' : '3'}, $${email.id ? '5' : '4'}, $${email.id ? '6' : '5'},
          $${email.id ? '7' : '6'}, $${email.id ? '8' : '7'}, $${email.id ? '9' : '8'}, $${email.id ? '10' : '9'}, $${email.id ? '11' : '10'}, $${email.id ? '12' : '11'}
        )
        RETURNING *
      `;

      // Prepare values array based on whether we have a predefined ID
      const emailValues = email.id
        ? [
            email.id,
            email.inbox_id,
            email.from_address,
            email.from_name,
            email.to_address,
            email.subject,
            email.text_content,
            email.html_content,
            email.headers ? JSON.stringify(email.headers) : null,
            email.has_attachments,
            email.is_deleted || false,
            email.spam_score || 0
          ]
        : [
            email.inbox_id,
            email.from_address,
            email.from_name,
            email.to_address,
            email.subject,
            email.text_content,
            email.html_content,
            email.headers ? JSON.stringify(email.headers) : null,
            email.has_attachments,
            email.is_deleted || false,
            email.spam_score || 0
          ];

      const emailResult = await client.query(emailQuery, emailValues);
      const createdEmail = emailResult.rows[0];

      // If there are attachments, insert them
      if (email.attachments && email.attachments.length > 0) {
        for (const attachment of email.attachments) {
          const attachmentQuery = `
            INSERT INTO attachments (
              email_id, filename, content_type, size, content_id, is_inline
            ) VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, filename, content_type, size, content_id, is_inline, created_at
          `;

          const attachmentValues = [
            createdEmail.id,
            attachment.filename,
            attachment.content_type,
            attachment.size,
            attachment.content_id,
            attachment.is_inline
          ];

          const attachmentResult = await client.query(attachmentQuery, attachmentValues);
          if (!createdEmail.attachments) {
            createdEmail.attachments = [];
          }
          createdEmail.attachments.push(attachmentResult.rows[0]);
        }
      }

      await client.query('COMMIT');
      return createdEmail;
    } catch (error: any) {
      if (client) {
        await client.query('ROLLBACK');
      }
      // Log the basic error first
      logger.error('Failed to create email', error);

      // Then log additional details as a separate message
      if (error.code || error.detail || error.constraint) {
        const details = {
          code: error.code,
          detail: error.detail,
          constraint: error.constraint
        };
        logger.error(`Additional database error details: ${JSON.stringify(details)}`);
      }

      // Log full serialized error for debugging
      try {
        const fullError = JSON.stringify(error, Object.getOwnPropertyNames(error));
        logger.error(`Full error serialization: ${fullError}`);
      } catch (err) {
        const serializationError = err instanceof Error ? err : new Error(String(err));
        logger.error('Could not serialize full error', serializationError);
      }
      throw new AppError('Failed to create email', 500);
    } finally {
      if (client) {
        client.release();
      }
    }
  }
}
