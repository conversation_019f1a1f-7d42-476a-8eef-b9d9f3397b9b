import express from 'express';
import * as emailController from '../controllers/emailController';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { validateParams, validateQuery } from '../middlewares/validator';
import { emailValidators } from '../validators/emailValidators';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { subscriptionRateLimiter } from '../middlewares/subscriptionRateLimiter';
import { inboxOwnershipAuth } from '../middlewares/inboxOwnershipAuth';
import { cacheMiddleware } from '../middlewares/cacheMiddleware';
import { noCacheMiddleware } from '../middlewares/noCacheMiddleware';
import logger from '../utils/logger';

const router = express.Router({ mergeParams: true });

// Apply authentication middleware that supports both RapidAPI and API key authentication
// This middleware first checks for RapidAPI authentication, and if that fails, it falls back to API key authentication
router.use(rapidApiAuth);

// Apply subscription-aware rate limiting after authentication
router.use(subscriptionRateLimiter);

// Verify inbox ownership after authentication
router.use(inboxOwnershipAuth);

// List emails for an inbox with caching
// CRITICAL FIX: Reduced TTL (60 seconds) for email lists to improve cross-region read status sync
// Each region has separate Redis cache, so shorter TTL ensures fresher data
router.get(
  '/',
  validateQuery(emailValidators.listEmails),
  cacheMiddleware(60, req => {
    // Create a cache key that includes the inbox ID and query parameters
    const inboxId = req.params.inboxId;
    const page = req.query.page || 1;
    const size = req.query.size || 10;
    const from = req.query.from || '';
    const subject = req.query.subject || '';
    const hasAttachments = req.query.hasAttachments || '';

    return `emails:inbox:${inboxId}:page:${page}:size:${size}:from:${from}:subject:${subject}:attachments:${hasAttachments}`;
  }),
  emailController.listEmails
);

// Get a specific email with caching
// CRITICAL FIX: Reduced TTL (30 seconds) for individual emails to improve cross-region read status sync
// Individual emails can change read status, so shorter TTL ensures fresher data across regions
router.get(
  '/:emailId',
  validateParams(emailValidators.getEmail),
  cacheMiddleware(30, req => {
    // Create a cache key that includes the inbox ID and email ID
    const inboxId = req.params.inboxId;
    const emailId = req.params.emailId;

    return `email:${inboxId}:${emailId}`;
  }),
  emailController.getEmail
);

// Get an email attachment - disabled for now
// router.get(
//   '/:emailId/attachments/:attachmentId',
//   validateParams(emailValidators.getAttachment),
//   emailController.getAttachment
// );

// Delete an email
router.delete(
  '/:emailId',
  validateParams(emailValidators.deleteEmail),
  (req, res, next) => {
    // Log the request parameters for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Delete email request params: inboxId=${req.params.inboxId}, emailId=${req.params.emailId}, path=${req.path}, originalUrl=${req.originalUrl}`);
    }
    next();
  },
  emailController.deleteEmail
);

export default router;


