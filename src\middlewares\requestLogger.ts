import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import healthCheck from '../utils/healthCheck';

// Attack pattern detection for request logging optimization
const COMMON_SCAN_PATTERNS = [
  '.env', '.git', 'wp-', 'admin', 'config', 'phpinfo', 'credentials',
  'aws', 'laravel', 'api/swagger', 'api/docs', 'actuator', 'prometheus',
  'metrics', 'health', 'status', 'debug', 'console', 'shell', 'cmd',
  'cgi-bin', 'phpmyadmin', 'mysql', 'db', 'database', 'backup',
  'wp-content', 'wp-admin', 'wp-login', 'xmlrpc.php', 'robots.txt',
  'sitemap.xml', 'favicon.ico'
];

// Attack mode state for request logging
let isRequestLogAttackMode = false;
let lastRequestLogAttackCheck = 0;
const REQUEST_LOG_ATTACK_DURATION = 300000; // 5 minutes

/**
 * Enable attack mode to disable request logging for scanning patterns
 */
export const enableRequestLogAttackMode = (): void => {
  if (!isRequestLogAttackMode) {
    isRequestLogAttackMode = true;
    console.log('[REQUEST LOG] ATTACK MODE ENABLED - disabling request logging for scanning patterns');
  }
  lastRequestLogAttackCheck = Date.now();
};

/**
 * Check if we should exit attack mode
 */
const checkRequestLogAttackModeExpiry = (): void => {
  if (isRequestLogAttackMode && (Date.now() - lastRequestLogAttackCheck > REQUEST_LOG_ATTACK_DURATION)) {
    isRequestLogAttackMode = false;
    console.log('[REQUEST LOG] ATTACK MODE DISABLED - resuming normal request logging');
  }
};

/**
 * Check if a request path matches common scanning patterns
 */
const isCommonScanPattern = (path: string): boolean => {
  return COMMON_SCAN_PATTERNS.some(pattern => path.includes(pattern));
};

/**
 * PHASE 2: Ultra-optimized request logger middleware
 * - Minimal overhead for all requests
 * - Complete removal of logging for high-traffic endpoints in production
 * - Only logs errors and critical operations
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  // Check attack mode expiry
  checkRequestLogAttackModeExpiry();

  // ULTRA-FAST PATH: Complete skip for high-traffic endpoints in production
  if (process.env.NODE_ENV === 'production' && (
      req.path === '/api/domains' ||
      req.path === '/api/inboxes' && req.method === 'GET' ||
      req.path.includes('/emails') ||
      req.path === '/health' ||
      req.path === '/healthz' ||
      req.path.startsWith('/static/'))) {
    return next();
  }

  // ATTACK MODE: Skip request logging for scanning patterns during attacks
  if (isRequestLogAttackMode && isCommonScanPattern(req.path)) {
    return next();
  }

  // In development, still skip some high-traffic endpoints
  if (process.env.NODE_ENV !== 'production' && (
      req.path === '/health' ||
      req.path === '/healthz' ||
      req.path.startsWith('/static/'))) {
    return next();
  }

  // PRODUCTION SAMPLING: Only log 1 in 1000 requests in production
  // During attack mode, reduce to 1 in 10000 to minimize overhead
  const samplingRate = isRequestLogAttackMode ? 0.0001 : 0.001;
  if (process.env.NODE_ENV === 'production' && Math.random() > samplingRate) {
    return next();
  }

  // Get start time - only for requests we're actually logging
  const start = Date.now();

  // Process the request
  next();

  // Log after response is sent - using once to prevent memory leaks
  res.once('finish', () => {
    // Calculate response time
    const responseTime = Date.now() - start;

    // PRODUCTION: Only log errors and extremely slow requests
    if (process.env.NODE_ENV === 'production') {
      // Only log server errors and extremely slow requests (>1000ms)
      if (res.statusCode >= 500 || responseTime > 1000) {
        // Use minimal logging format
        const logMessage = `${req.method} ${req.path} ${res.statusCode} ${responseTime}ms`;

        if (res.statusCode >= 500) {
          logger.error(logMessage);
        } else {
          logger.warn(logMessage);
        }
      }
    }
    // DEVELOPMENT: More detailed logging but still optimized
    else {
      // Log errors and slow requests
      if (res.statusCode >= 400 || responseTime > 500) {
        logger.info(`${req.method} ${req.path} - ${res.statusCode} - ${responseTime}ms`);
      }
    }

    // Record response time in the background without blocking
    if (responseTime > 500) {
      setTimeout(() => {
        try {
          healthCheck.recordResponseTime(responseTime);
        } catch (e) {
          // Ignore errors in background processing
        }
      }, 0);
    }
  });
};
