import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import logger from '../utils/logger';

/**
 * Admin authentication middleware
 * 
 * This middleware checks for the ADMIN_API_KEY in the request headers
 * and verifies it against the environment variable.
 * 
 * It provides an extra layer of security for admin-only endpoints.
 */
export const adminAuth = (req: Request, res: Response, next: NextFunction) => {
  try {
    const adminApiKey = process.env.ADMIN_API_KEY;
    
    // If ADMIN_API_KEY is not set in environment variables, deny access
    if (!adminApiKey) {
      logger.error('ADMIN_API_KEY is not set in environment variables');
      throw new AppError('Admin API is not configured', 501);
    }
    
    // Get the API key from the request headers
    const providedApiKey = req.headers['x-admin-api-key'] as string;
    
    // If no API key is provided, deny access
    if (!providedApiKey) {
      throw new AppError('Admin API key is required', 401);
    }
    
    // If the provided API key doesn't match the expected one, deny access
    if (providedApiKey !== adminApiKey) {
      logger.warn(`Invalid admin API key attempt: ${providedApiKey.substring(0, 8)}...`);
      throw new AppError('Invalid admin API key', 403);
    }
    
    // If we get here, the API key is valid
    next();
  } catch (error) {
    next(error);
  }
};
