import { Request, Response, NextFunction } from 'express';
import { ApiKeyModel } from '../models/ApiKey';
import { AppError } from './errorHandler';
import auditLogger from '../utils/auditLogger';
import logger from '../utils/logger';

// Extend Express Request interface to include a<PERSON><PERSON><PERSON>
declare global {
  namespace Express {
    interface Request {
      apiKey?: any;
    }
  }
}

/**
 * API Key authentication middleware
 */
export const apiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get API key from header
    const apiKey = req.headers['x-api-key'] as string;

    // Debug log for instance 2 or if DEBUG_AUTH is enabled
    if (process.env.INSTANCE_ID === '2' || process.env.DEBUG_AUTH === 'true') {
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] API Key Auth Check:`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Request API key present: ${!!apiKey}`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Request path: ${req.path}`);
      logger.debug(`[Instance ${process.env.INSTANCE_ID}] Request method: ${req.method}`);
    }

    // Check if API key is provided
    if (!apiKey) {
      // Log failed authentication attempt
      auditLogger.logSecurityEvent(req, 'authentication', { message: 'API key missing' }, false);
      logger.warn(`[Instance ${process.env.INSTANCE_ID}] API key missing for path: ${req.path}`);
      return next(new AppError('API key is required', 401));
    }

    // Validate API key with subscription information
    const apiKeyData = await ApiKeyModel.getByKeyWithSubscription(apiKey);

    // Check if API key exists and is active
    if (!apiKeyData) {
      // Log failed authentication attempt
      auditLogger.logSecurityEvent(req, 'authentication', { message: 'Invalid API key' }, false);
      return next(new AppError('Invalid API key', 401));
    }

    // Check if API key is expired
    if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
      // Log failed authentication attempt
      auditLogger.logSecurityEvent(req, 'authentication', { message: 'Expired API key', keyId: apiKeyData.id }, false);
      return next(new AppError('API key has expired', 401));
    }

    // Track API key usage
    await ApiKeyModel.trackUsage(apiKey);

    // Attach API key data to request
    req.apiKey = apiKeyData;

    // Log successful authentication
    auditLogger.logSecurityEvent(req, 'authentication', { keyId: apiKeyData.id }, true);

    // Continue to next middleware
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional API Key authentication middleware
 * Will attach API key data to request if provided, but won't require it
 */
export const optionalApiKeyAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get API key from header
    const apiKey = req.headers['x-api-key'] as string;

    // If no API key is provided, continue
    if (!apiKey) {
      return next();
    }

    // Validate API key with subscription information
    const apiKeyData = await ApiKeyModel.getByKeyWithSubscription(apiKey);

    // If API key is valid and active, attach to request
    if (apiKeyData && (!apiKeyData.expires_at || new Date(apiKeyData.expires_at) >= new Date())) {
      // Track API key usage
      await ApiKeyModel.trackUsage(apiKey);

      // Attach API key data to request
      req.apiKey = apiKeyData;
    }

    // Continue to next middleware
    next();
  } catch (error) {
    next(error);
  }
};
