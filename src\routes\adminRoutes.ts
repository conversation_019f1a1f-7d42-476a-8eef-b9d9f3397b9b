import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { InboxModel } from '../models/Inbox';
import { ApiKeyModel, SubscriptionType } from '../models/ApiKey';
import { adminAuth } from '../middlewares/adminAuth';
import { validate, validateParams } from '../middlewares/validator';
import { createUserApiKeySchema, updateApiKeySubscriptionSchema, apiKeyIdSchema } from '../validators/adminValidators';
import { AppError } from '../middlewares/errorHandler';
import logger from '../utils/logger';

const router = express.Router();

// Apply admin authentication middleware to all routes
router.use(adminAuth);

/**
 * POST /api/admin/keys - Create a new user API key
 * This endpoint solves the bootstrap problem by allowing admins to create API keys
 * for users without requiring an existing API key
 */
router.post('/keys', validate(createUserApiKeySchema), async (req, res, next) => {
  try {
    const { name, expiresIn, subscriptionType } = req.body;

    // Calculate expiration date if expiresIn is provided (in days)
    let expiresAt: Date | null = null;
    if (expiresIn && typeof expiresIn === 'number' && expiresIn > 0) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresIn);
    }

    // Create API key with subscription type
    const apiKey = await ApiKeyModel.create(name, expiresAt, subscriptionType as SubscriptionType);

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    // Log the API key creation for audit purposes
    logger.info(`Admin created API key: ${apiKey.id} with subscription: ${subscriptionType}`);

    res.status(201).json({
      'request-id': requestId,
      'message': 'Success',
      data: {
        apiKey: {
          id: apiKey.id,
          key: apiKey.key,
          name: apiKey.name,
          created_at: apiKey.created_at,
          expires_at: apiKey.expires_at,
          subscription_type: apiKey.subscription_type,
          rate_limit_tier: apiKey.rate_limit_tier,
        },
      },
    });
  } catch (error) {
    logger.error('Error creating user API key via admin endpoint:', error instanceof Error ? error : new Error(String(error)));
    next(error);
  }
});

/**
 * PATCH /api/admin/keys/:id/subscription - Update API key subscription
 * Allows admins to upgrade/downgrade user subscriptions
 */
router.patch('/keys/:id/subscription',
  validateParams(apiKeyIdSchema),
  validate(updateApiKeySubscriptionSchema),
  async (req, res, next) => {
    try {
      const { id } = req.params;
      const { subscriptionType } = req.body;

      // Check if API key exists
      const existingKey = await ApiKeyModel.getById(id);
      if (!existingKey) {
        return next(new AppError('API key not found', 404));
      }

      // Update subscription
      const updated = await ApiKeyModel.updateSubscription(id, subscriptionType as SubscriptionType);

      if (!updated) {
        return next(new AppError('Failed to update API key subscription', 500));
      }

      // Get updated API key data
      const updatedKey = await ApiKeyModel.getByKeyWithSubscription(existingKey.key);

      // Get request ID from middleware or generate new one
      const requestId = (req as any).requestId || uuidv4().toUpperCase();

      // Log the subscription update for audit purposes
      logger.info(`Admin updated API key subscription: ${id} to ${subscriptionType}`);

      res.status(200).json({
        'request-id': requestId,
        'message': 'Subscription updated successfully',
        data: {
          apiKey: {
            id: updatedKey?.id,
            name: updatedKey?.name,
            subscription_type: updatedKey?.subscription_type,
            rate_limit_tier: updatedKey?.rate_limit_tier,
            updated_at: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      logger.error('Error updating API key subscription:', error instanceof Error ? error : new Error(String(error)));
      next(error);
    }
  }
);

/**
 * GET /api/admin/maintenance/status
 * Get the status of maintenance jobs
 */
router.get('/maintenance/status', async (req, res, next) => {
  try {
    const status = await InboxModel.getCleanupJobStatus();

    // Get request ID from middleware or generate new one
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    res.status(200).json({
      'request-id': requestId,
      'message': 'Success',
      data: status
    });
  } catch (error) {
    const errorMessage = 'Error getting maintenance status:';
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error(errorMessage, errorObj);
    next(error);
  }
});

/**
 * POST /api/admin/maintenance/run-cleanup
 * Manually trigger the inbox cleanup process
 */
router.post('/maintenance/run-cleanup', async (req, res, next) => {
  try {
    const startTime = Date.now();
    const affectedRows = await InboxModel.cleanupExpired();
    const executionTime = Date.now() - startTime;

    res.status(200).json({
      status: 'success',
      message: `Cleanup completed successfully`,
      data: {
        affectedRows,
        executionTime: `${executionTime}ms`
      }
    });
  } catch (error) {
    const errorMessage = 'Error running manual cleanup:';
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logger.error(errorMessage, errorObj);
    next(error);
  }
});

export default router;
