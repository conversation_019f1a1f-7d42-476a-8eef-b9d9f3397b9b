import { Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import dotenv from 'dotenv';
import { AppError } from './errorHandler';
import { isRedisConnected } from '../config/redis-direct';
import logger from '../utils/logger';

// Load environment variables
dotenv.config();

// Import default rate limits from config
import { DEFAULT_RATE_LIMITS } from '../config/limits';

// Default window for rate limiting (from config or environment variable)
const DEFAULT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || DEFAULT_RATE_LIMITS.WINDOW_MS.toString());

// Default max requests (from config or environment variable)
const DEFAULT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || DEFAULT_RATE_LIMITS.MAX_REQUESTS.toString());

// Factory function to create a new store instance for each rate limiter
const createDynamicStore = (prefix: string = 'rate-limit') => new class DynamicStore {
  async increment(key: string): Promise<{ totalHits: number, resetTime: Date }> {
    // Use memory store only - Redis API doesn't support rate limiting yet
    // Simple in-memory implementation
    const now = Date.now();
    const windowMs = DEFAULT_WINDOW_MS;
    const resetTime = new Date(now + windowMs);

    // This is a simplified version - in production you'd want a proper memory store
    // that cleans up old entries
    if (!this.hits) this.hits = {};
    if (!this.hits[key]) this.hits[key] = 0;
    this.hits[key]++;

    return {
      totalHits: this.hits[key],
      resetTime
    };
  }

  // Store hits in memory as fallback
  private hits: Record<string, number> = {};

  // Implement other required methods
  async decrement(key: string): Promise<void> {
    // Memory implementation
    if (this.hits && this.hits[key] && this.hits[key] > 0) {
      this.hits[key]--;
    }
  }

  async resetKey(key: string): Promise<void> {
    // Memory implementation
    if (this.hits) {
      delete this.hits[key];
    }
  }
};

// Default rate limit configuration
const defaultRateLimitConfig = {
  windowMs: DEFAULT_WINDOW_MS, // 1 minute by default
  max: DEFAULT_MAX_REQUESTS, // 10 requests per second (600 per minute) by default
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: 'Too many requests, please try again later.',
  handler: (_req: Request, _res: Response, next: NextFunction, options: any) => {
    // Create a custom error for the rate limit
    next(new AppError(options.message, 429));
  },
  // Create a new dynamic store instance for this rate limiter
  store: createDynamicStore('general'),
};

// Helper function to check if a request is from RapidAPI with a valid Proxy Secret
// This is a strict security check to ensure only legitimate RapidAPI requests bypass rate limiting
const isRapidApiRequest = (req: Request): boolean => {
  // Check for internal API requests (always skip rate limiting for internal requests)
  if (req.headers['x-api-key'] === process.env.INTERNAL_API_KEY) {
    return true;
  }

  // PRIMARY CHECK: Check for a valid RapidAPI Proxy Secret (strongest authentication)
  const rapidApiProxySecret = (req.headers['x-rapidapi-proxy-secret'] ||
                             req.headers['X-RapidAPI-Proxy-Secret'] || '') as string;

  if (rapidApiProxySecret && process.env.RAPIDAPI_PROXY_SECRET &&
      rapidApiProxySecret === process.env.RAPIDAPI_PROXY_SECRET) {
    // This is a verified RapidAPI request with the correct proxy secret
    return true;
  }

  // SECONDARY CHECK: If req.rapidApi is set by the rapidApiAuth middleware, it's a RapidAPI request
  // that has already been authenticated with a valid Proxy Secret
  if ((req as any).rapidApi) {
    return true;
  }

  // All other requests are subject to rate limiting
  return false;
};

// Log that we're initializing rate limiters
logger.info('Initializing rate limiters with memory store');

// Create a general rate limiter middleware
export const generalRateLimiter = rateLimit({
  ...defaultRateLimitConfig,
  store: createDynamicStore('general'),
  // Skip rate limiting for RapidAPI requests
  skip: isRapidApiRequest
});

// Create a more strict rate limiter for sensitive endpoints
export const strictRateLimiter = rateLimit({
  ...defaultRateLimitConfig,
  windowMs: 60000, // 1 minute
  max: 300, // 5 requests per second (300 per minute)
  store: createDynamicStore('strict'),
  // Use the same skip function as the general rate limiter
  skip: isRapidApiRequest
});

// Create a custom rate limiter factory function
export const createRateLimiter = (options: any) => {
  // Generate a unique prefix for this rate limiter
  const prefix = options.keyGenerator ?
    `custom-${Math.random().toString(36).substring(2, 10)}` :
    'custom';

  return rateLimit({
    ...defaultRateLimitConfig,
    ...options,
    store: createDynamicStore(prefix),
  });
};

// Create endpoint-specific rate limiters
export const apiKeyRateLimiter = createRateLimiter({
  // Use a specific prefix for this rate limiter
  store: createDynamicStore('api-key'),
  windowMs: 60000, // 1 minute
  max: 120, // 2 requests per second (120 per minute)
  keyGenerator: (req: Request) => {
    // Use API key as the rate limit key if available
    const apiKey = req.headers['x-api-key'] as string;
    if (apiKey) {
      return `api-key:${apiKey}`;
    }
    // Fall back to IP address
    return `ip:${req.ip}`;
  },
});

// IP-based rate limiter
export const ipRateLimiter = createRateLimiter({
  // Use a specific prefix for this rate limiter
  store: createDynamicStore('ip'),
  keyGenerator: (req: Request) => `ip:${req.ip}`,
});

// Export a default rate limiter
export default generalRateLimiter;
