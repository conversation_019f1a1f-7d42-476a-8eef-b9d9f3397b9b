// src/routes/internalRoutes.ts
import express from 'express';
import { internalApi<PERSON><PERSON><PERSON><PERSON>, enhancedInternalApi<PERSON>eyAuth } from '../middlewares/internalApiKeyAuth';
import * as internalController from '../controllers/internalController';
import logger from '../utils/logger';

const router = express.Router();

// IP restriction has been removed as it's now handled by Cloudflare WAF rules
logger.info('Internal routes IP restriction is now handled by Cloudflare WAF rules');

// Configure slightly higher limits for internal routes (for email attachments)
const highLimitJsonParser = express.json({ limit: '10mb' });

// Endpoint to receive emails from SMTP server with enhanced authentication
// Uses API key in query parameter instead of header
router.post('/emails', highLimitJsonParser, enhancedInternalApiKeyAuth, internalController.processEmail);

// Endpoint to check if an inbox exists (legacy authentication)
router.get('/inbox/:email', internalApiKeyAuth, internalController.getInboxInfo as express.RequestHandler);

export default router;
